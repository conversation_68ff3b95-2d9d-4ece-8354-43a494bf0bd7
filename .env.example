# Field ID - Environment Variables Example
# Copy this file to .env.local and fill in your actual values

# Google AI API Key for Genkit integration
# Get your API key from: https://aistudio.google.com/app/apikey
GOOGLE_GENAI_API_KEY=your_google_ai_api_key_here

# Node Environment (development, production, test)
NODE_ENV=development

# Next.js Configuration
# NEXT_PUBLIC_ variables are exposed to the browser
# NEXT_PUBLIC_APP_URL=https://your-domain.com

# Optional: Custom model URL if hosting model externally
# NEXT_PUBLIC_MODEL_URL=/model.onnx

# Optional: Analytics or monitoring keys
# NEXT_PUBLIC_ANALYTICS_ID=your_analytics_id

# Optional: Error reporting
# SENTRY_DSN=your_sentry_dsn

# Firebase Configuration (if using Firebase features)
# FIREBASE_PROJECT_ID=your_project_id
# FIREBASE_CLIENT_EMAIL=your_client_email
# FIREBASE_PRIVATE_KEY=your_private_key

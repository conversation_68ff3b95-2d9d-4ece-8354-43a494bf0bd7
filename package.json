{"name": "field-id", "version": "1.0.0", "description": "AI-powered sports player tracking and identification in videos", "private": true, "keywords": ["ai", "sports", "player-tracking", "yolo", "onnx", "nextjs"], "author": "Field ID Team", "license": "MIT", "scripts": {"dev": "next dev --turbopack -p 9002", "genkit:dev": "genkit start -- tsx src/ai/dev.ts", "genkit:watch": "genkit start -- tsx --watch src/ai/dev.ts", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "build:analyze": "ANALYZE=true npm run build", "export": "next export", "deploy:vercel": "vercel --prod", "deploy:netlify": "netlify deploy --prod", "postbuild": "echo 'Build completed successfully!'", "clean": "rm -rf .next out dist", "security-check": "node scripts/security-check.js", "pre-commit": "npm run security-check && npm run typecheck", "download-model": "node scripts/download-model.js", "setup": "npm install && npm run download-model"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"@genkit-ai/googleai": "^1.13.0", "@genkit-ai/next": "^1.13.0", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "firebase": "^11.9.1", "genkit": "^1.13.0", "lucide-react": "^0.475.0", "next": "15.3.3", "next-themes": "^0.4.6", "onnxruntime-web": "^1.22.0", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.3", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "genkit-cli": "^1.13.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}
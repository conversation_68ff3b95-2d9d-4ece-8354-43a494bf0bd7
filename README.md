# Field ID - AI-Powered Sports Player Tracking

Field ID is an intelligent web application that uses AI to detect, track, and identify players in sports videos. Built with Next.js and powered by YOLOv11 object detection, it provides real-time player tracking with advanced features like LLM-assisted re-identification and activity summarization.

![Field ID Logo](https://img.shields.io/badge/Field%20ID-AI%20Player%20Tracking-blue?style=for-the-badge)
![Next.js](https://img.shields.io/badge/Next.js-15.3.3-black?style=flat-square&logo=next.js)
![Streamlit](https://img.shields.io/badge/Streamlit-1.45.1-red?style=flat-square&logo=streamlit)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=flat-square&logo=typescript)
![Python](https://img.shields.io/badge/Python-3.13-green?style=flat-square&logo=python)

## 🎯 Features

### 🌐 Dual Application Architecture
This project includes **two complementary applications**:

#### **Next.js Web Application** (Production-Ready)
- **🎥 Real-time Player Detection**: Uses YOLOv11 ONNX model for accurate player and ball detection
- **🔍 Advanced Player Tracking**: Sophisticated tracking algorithms that follow players across frames
- **🤖 AI-Powered Re-identification**: LLM-assisted player re-identification when players leave/re-enter the frame
- **📊 Live Statistics**: Real-time analytics showing total players, active players, and inactive players
- **📝 Activity Summaries**: AI-generated summaries of game activity using Google's Gemini AI
- **🎨 Visual Overlay**: Real-time bounding boxes and player IDs overlaid on video
- **📱 Responsive Design**: Modern UI that works on desktop and mobile devices
- **🌙 Dark/Light Mode**: Theme support for user preference

#### **Streamlit Dashboard** (Analytics & Visualization)
- **📊 Advanced Analytics Dashboard**: Interactive charts, heatmaps, and performance metrics
- **🎯 Simulation-Based Demo**: Mock AI system for demonstration without heavy dependencies
- **📈 Real-time Visualizations**: Plotly-powered interactive charts and graphs
- **🤖 AI Insights Panel**: Smart analysis with tactical recommendations
- **💾 Data Export**: Multiple export formats (JSON, CSV, Excel, PDF)
- **🎨 Modern UI**: Professional gradient design with smooth animations

## 🛠️ Technology Stack

- **Frontend**: Next.js 15.3.3 with TypeScript
- **UI Framework**: React 18 with Radix UI components
- **Styling**: Tailwind CSS with custom theme
- **AI Integration**:
  - ONNX Runtime Web for player detection
  - Google Genkit with Gemini 2.0 Flash for LLM features
- **State Management**: Zustand (for future enhancements)
- **Icons**: Lucide React

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js**: Version 18.0 or higher
- **npm**: Version 8.0 or higher (comes with Node.js)
- **Python**: Version 3.8 or higher (for model conversion)
- **Git**: For cloning the repository

### System Requirements

- **RAM**: Minimum 8GB (16GB recommended for smooth video processing)
- **Storage**: At least 2GB free space
- **Browser**: Modern browser with WebAssembly support (Chrome, Firefox, Safari, Edge)

## 🚀 Installation

### 1. Clone the Repository

```bash
git clone <repository-url>
cd field-id
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Set Up Environment Variables

Create a `.env.local` file in the root directory:

```env
# Google AI API Key for Genkit integration
# Get your API key from: https://aistudio.google.com/app/apikey
GOOGLE_GENAI_API_KEY=your_google_ai_api_key_here

# Optional: Set to development for local development
NODE_ENV=development
```

### 4. Model Setup

The application requires a YOLOv11 model in ONNX format.

#### Option A: Automatic Download (Recommended)

```bash
npm run download-model
```

This will automatically download the pre-trained model to the correct location.

#### Option B: Manual Download

If automatic download fails, manually download the model:

1. Download the model file from: [Model Release Page](https://github.com/your-username/field-id-models/releases)
2. Place the `model.onnx` file in the `public/` directory

#### Option C: Convert Your Own Model

If you have a PyTorch model (`best.pt`):

1. Install required Python packages:
```bash
pip install ultralytics torch onnx
```

2. Convert the model:
```python
from ultralytics import YOLO

# Load the model
model = YOLO('best.pt')

# Export to ONNX
model.export(format='onnx', imgsz=640, dynamic=False, simplify=True, opset=11)

# Move the resulting file to public/model.onnx
```

### 5. Verify Setup

Run the type checker to ensure everything is configured correctly:

```bash
npm run typecheck
```

## 🎮 Usage

### 🌐 Running Both Applications

This project provides two ways to experience Field ID:

#### **Option 1: Next.js Web Application (Full AI Features)**

```bash
npm run dev
```

The application will be available at `http://localhost:9002`

#### **Option 2: Streamlit Dashboard (Analytics Focus)**

```bash
# Install Python dependencies (if not already installed)
pip install -r requirements.txt

# Run Streamlit app
python -m streamlit run streamlit_app.py
```

The dashboard will be available at `http://localhost:8501`

#### **Option 3: Run Both Simultaneously**

You can run both applications at the same time for the complete experience:

1. **Terminal 1** (Next.js):
   ```bash
   npm run dev
   ```

2. **Terminal 2** (Streamlit):
   ```bash
   python -m streamlit run streamlit_app.py
   ```

- **Next.js App**: http://localhost:9002 (Full AI implementation)
- **Streamlit Dashboard**: http://localhost:8501 (Analytics & visualization)

### Using the Application

1. **Upload a Video**:
   - Click the "Select File" button in the sidebar
   - Choose a sports video file (MP4, AVI, MOV, etc.)
   - The video will load in the main player area

2. **Start Player Tracking**:
   - Click the "Start Tracking" button
   - The AI model will begin detecting players and sports balls
   - Real-time bounding boxes will appear around detected objects

3. **View Statistics**:
   - The sidebar shows live statistics:
     - Total players detected
     - Currently active players
     - Inactive players
   - Statistics update in real-time as the video plays

4. **Generate Activity Summary**:
   - Click the "Generate Summary" button
   - The AI will analyze the tracked activity and provide insights
   - Summaries include player movements, game events, and key moments

5. **Download Results**:
   - Use the download button to save tracking data
   - Export includes player positions, timestamps, and activity logs

### Supported Video Formats

- MP4 (recommended)
- AVI
- MOV
- WebM
- MKV

### Performance Tips

- **Video Resolution**: 720p-1080p works best for optimal performance
- **Video Length**: Shorter videos (under 10 minutes) process faster
- **Browser**: Use Chrome or Edge for best WebAssembly performance

## 🔧 Development

### Available Scripts

```bash
# Start development server
npm run dev

# Start Genkit development server (for AI features)
npm run genkit:dev

# Type checking
npm run typecheck

# Build for production
npm run build

# Start production server
npm start

# Lint code
npm run lint
```

### Project Structure

```
field-id/
├── src/
│   ├── ai/                 # AI integration and flows
│   │   ├── flows/          # Genkit AI flows
│   │   ├── genkit.ts       # Genkit configuration
│   │   └── dev.ts          # Development server
│   ├── app/                # Next.js app directory
│   │   ├── page.tsx        # Main application page
│   │   ├── layout.tsx      # Root layout
│   │   └── globals.css     # Global styles
│   ├── components/         # React components
│   │   ├── ui/             # UI components
│   │   ├── common/         # Common components
│   │   ├── field-id-logo.tsx
│   │   └── video-player.tsx
│   ├── hooks/              # Custom React hooks
│   ├── lib/                # Utility functions and types
│   └── types/              # TypeScript type definitions
├── public/                 # Static assets
│   ├── model.onnx          # YOLO model file
│   └── onnx-wasm/          # ONNX Runtime WASM files
├── docs/                   # Documentation
└── package.json            # Dependencies and scripts
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Model Loading Errors

**Problem**: "Could not load model.onnx"

**Solutions**:
- Ensure `public/model.onnx` exists and is a valid ONNX file
- Check that the model was exported with the correct parameters:
  - Input shape: [1, 3, 640, 640]
  - ONNX opset version 11 or higher
- Verify the model file isn't corrupted (should be 20-50MB)

#### 2. WASM Loading Errors

**Problem**: "Failed to fetch WASM files"

**Solutions**:
- Ensure `public/onnx-wasm/` directory contains the required WASM files
- Check browser console for specific WASM loading errors
- Try clearing browser cache and reloading
- Verify your browser supports WebAssembly

#### 3. Video Upload Issues

**Problem**: Video won't load or play

**Solutions**:
- Check video format is supported (MP4 recommended)
- Ensure video file isn't corrupted
- Try a smaller video file (under 100MB)
- Check browser console for media loading errors

#### 4. Performance Issues

**Problem**: Slow tracking or browser freezing

**Solutions**:
- Use smaller video resolution (720p or lower)
- Close other browser tabs to free up memory
- Ensure your system meets minimum requirements
- Try using Chrome or Edge for better performance

#### 5. AI Features Not Working

**Problem**: Summary generation fails

**Solutions**:
- Verify `GOOGLE_GENAI_API_KEY` is set in `.env.local`
- Check that the API key is valid and has sufficient quota
- Ensure internet connection is stable
- Check browser console for API errors

### Getting Help

If you encounter issues not covered here:

1. Check the browser console for error messages
2. Verify all prerequisites are installed correctly
3. Ensure environment variables are properly configured
4. Try with a different video file to isolate the issue

### Performance Optimization

For better performance:

- Use hardware acceleration in your browser
- Close unnecessary applications to free up system resources
- Use videos with consistent frame rates
- Consider using a dedicated GPU for better processing

## 🚀 Deployment

Field ID can be deployed to various platforms. Here are the recommended deployment options:

### Vercel (Recommended)

Vercel provides the best experience for Next.js applications:

1. **Fork/Clone the repository** to your GitHub account

2. **Install Vercel CLI** (optional):
   ```bash
   npm install -g vercel
   ```

3. **Deploy via Vercel Dashboard**:
   - Go to [vercel.com](https://vercel.com)
   - Import your GitHub repository
   - Configure environment variables:
     - `GOOGLE_GENAI_API_KEY`: Your Google AI API key

4. **Deploy via CLI**:
   ```bash
   npm run deploy:vercel
   ```

### Netlify

Alternative deployment option:

1. **Connect your repository** to Netlify
2. **Build settings**:
   - Build command: `npm run build`
   - Publish directory: `.next`
3. **Environment variables**:
   - Add `GOOGLE_GENAI_API_KEY` in Netlify dashboard

### GitHub Pages (Static Export)

For static deployment without server-side features:

```bash
npm run build
npm run export
```

### Environment Variables for Production

Set these environment variables in your deployment platform:

```env
# Required
GOOGLE_GENAI_API_KEY=your_google_ai_api_key_here

# Optional
NEXT_PUBLIC_MODEL_URL=https://your-cdn.com/model.onnx
NODE_ENV=production
```

### Model File Considerations

The ONNX model file (`model.onnx`) is approximately 389MB. For production deployment:

**Option 1: Include in Repository**
- Suitable for platforms with generous file size limits
- Slower initial deployment and cloning

**Option 2: External Hosting (Recommended)**
- Host the model file on a CDN (AWS S3, Google Cloud Storage, etc.)
- Set `NEXT_PUBLIC_MODEL_URL` environment variable
- Faster deployments and better performance

**Option 3: Git LFS**
- Use Git Large File Storage for the model file
- Good balance between convenience and performance

### Performance Optimization

For production deployment:

1. **Enable compression** in your hosting platform
2. **Use CDN** for static assets
3. **Host model externally** for faster loading
4. **Enable caching** for ONNX and WASM files

### Deployment Checklist

- [ ] Environment variables configured
- [ ] Model file accessible (local or external)
- [ ] HTTPS enabled (required for WASM)
- [ ] CORS headers configured for external model
- [ ] Build process tested locally
- [ ] Error monitoring configured (optional)

## 🐛 Troubleshooting Deployment

### Common Issues

**Build Failures:**
- Ensure Node.js version >= 18
- Check environment variables are set
- Verify all dependencies are installed

**Model Loading Errors:**
- Check model file accessibility
- Verify HTTPS is enabled
- Ensure CORS headers for external models

**WASM Loading Issues:**
- Confirm HTTPS deployment
- Check Cross-Origin headers
- Verify browser compatibility

**Performance Issues:**
- Use external model hosting
- Enable CDN for static assets
- Optimize image and video files

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

### Development Setup

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

### Reporting Issues

When reporting issues, please include:
- Browser and version
- Operating system
- Steps to reproduce
- Error messages or screenshots

## 📞 Support

For support and questions:
- 🐛 **Bug Reports**: [GitHub Issues](https://github.com/your-username/field-id/issues)
- 💬 **Discussions**: [GitHub Discussions](https://github.com/your-username/field-id/discussions)
- 📧 **Email**: <EMAIL> (if applicable)

## 🔗 Links

- **Live Demo**: [https://field-id.vercel.app](https://field-id.vercel.app)
- **Documentation**: [GitHub Wiki](https://github.com/your-username/field-id/wiki)
- **Changelog**: [CHANGELOG.md](./CHANGELOG.md)
## 🚀 Quick Start Guide

### 1. Clone the Repository

```bash
git clone <your-repository-url>
cd field-id-sports-tracking
```

### 2. Setup Environment Variables

```bash
# Copy the example environment file
cp .env.example .env.local

# Edit .env.local and add your Google AI API key
# Get your API key from: https://aistudio.google.com/app/apikey
```

### 3. Install Dependencies

```bash
# Install Node.js dependencies
npm install

# Install Python dependencies
pip install -r requirements.txt
```

### 4. Download Required Models

```bash
# Download the YOLO model (for Next.js app)
npm run download-model
```

### 5. Run the Applications

Choose your preferred option:

**Next.js (Full AI Features):**
```bash
npm run dev
# Open http://localhost:9002
```

**Streamlit (Analytics Dashboard):**
```bash
python -m streamlit run streamlit_app.py
# Open http://localhost:8501
```

## 📁 Repository Structure

This repository contains:
- **Next.js Application**: Full-featured web app with AI model integration
- **Streamlit Dashboard**: Analytics-focused dashboard with simulation
- **Shared Documentation**: Comprehensive setup and deployment guides
- **AI Integration**: Google Genkit flows for LLM features
- **Modern UI Components**: Radix UI and Tailwind CSS styling

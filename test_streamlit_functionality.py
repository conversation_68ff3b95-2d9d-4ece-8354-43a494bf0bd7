#!/usr/bin/env python3
"""
Test script to verify Streamlit application functionality
"""

import requests
import time
import sys
import json
from datetime import datetime

def test_server_availability():
    """Test if the Streamlit server is running and accessible"""
    try:
        response = requests.get('http://localhost:8501', timeout=10)
        if response.status_code == 200:
            print("✅ Server is running and accessible")
            return True
        else:
            print(f"❌ Server returned status code: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server - server may not be running")
        return False
    except requests.exceptions.Timeout:
        print("❌ Server request timed out")
        return False
    except Exception as e:
        print(f"❌ Error testing server: {str(e)}")
        return False

def test_imports():
    """Test if all required imports work correctly"""
    try:
        import streamlit as st
        import numpy as np
        import pandas as pd
        import plotly.express as px
        import plotly.graph_objects as go
        print("✅ All required imports successful")
        return True
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
        return False

def test_streamlit_app_syntax():
    """Test if the streamlit app has valid syntax"""
    try:
        import py_compile
        py_compile.compile('streamlit_app.py', doraise=True)
        print("✅ Streamlit app syntax is valid")
        return True
    except py_compile.PyCompileError as e:
        print(f"❌ Syntax error in streamlit_app.py: {str(e)}")
        return False

def test_mock_systems():
    """Test if mock systems can be initialized"""
    try:
        # Import the app module to test mock systems
        import streamlit_app
        
        # Test MockVideoProcessor
        processor = streamlit_app.MockVideoProcessor()
        test_detection = processor.simulate_player_detection(100)
        
        if 'players' in test_detection and 'frame_number' in test_detection:
            print("✅ MockVideoProcessor working correctly")
        else:
            print("❌ MockVideoProcessor not returning expected data")
            return False
            
        # Test MockAIAnalyzer
        analyzer = streamlit_app.MockAIAnalyzer()
        test_analysis = analyzer.analyze_game_flow([test_detection])
        
        if 'formation' in test_analysis and 'coordination_score' in test_analysis:
            print("✅ MockAIAnalyzer working correctly")
        else:
            print("❌ MockAIAnalyzer not returning expected data")
            return False
            
        return True
    except Exception as e:
        print(f"❌ Error testing mock systems: {str(e)}")
        return False

def test_configuration_files():
    """Test if configuration files are valid"""
    try:
        import toml
        
        # Test .streamlit/config.toml
        with open('.streamlit/config.toml', 'r') as f:
            config = toml.load(f)
            
        if 'server' in config and 'theme' in config:
            print("✅ Streamlit configuration is valid")
        else:
            print("❌ Streamlit configuration missing required sections")
            return False
            
        return True
    except Exception as e:
        print(f"❌ Error testing configuration: {str(e)}")
        return False

def run_all_tests():
    """Run all functionality tests"""
    print("🧪 Starting Streamlit Application Functionality Tests")
    print("=" * 60)
    
    tests = [
        ("Import Dependencies", test_imports),
        ("App Syntax Check", test_streamlit_app_syntax),
        ("Mock Systems", test_mock_systems),
        ("Configuration Files", test_configuration_files),
        ("Server Availability", test_server_availability),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Testing: {test_name}")
        if test_func():
            passed += 1
        else:
            print(f"   Test failed: {test_name}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Application is ready for deployment.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)

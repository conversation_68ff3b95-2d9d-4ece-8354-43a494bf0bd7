'use client'

import * as React from 'react'
import * as ort from 'onnxruntime-web'
import {
  Download,
  Loader,
  MessageSquareQuote,
  Play,
  StopCircle,
  UploadCloud,
  UserCheck,
  UserX,
  Users,
} from 'lucide-react'

import { useToast } from '@/hooks/use-toast'
import { cn } from '@/lib/utils'
import type { BoundingBox, FrameData, Player, TrackingData } from '@/lib/types'
import { llmAssistedPlayerReidentification } from '@/ai/flows/llm-assisted-player-reidentification'
import { summarizeActivity } from '@/ai/flows/summarize-activity'
import { FieldIdLogo } from '@/components/field-id-logo'
import { VideoPlayer } from '@/components/video-player'
import { DarkModeToggle } from '@/components/common/dark-mode-toggle'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Progress } from '@/components/ui/progress'
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '@/components/ui/sidebar'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

const MODEL_URL = process.env.NEXT_PUBLIC_MODEL_URL || '/model.onnx'
const MODEL_INPUT_SHAPE = [1, 3, 640, 640]
const FPS = 30 // Target FPS for processing

// Detection threshold
const CONFIDENCE_THRESHOLD = 0.5
const IOU_THRESHOLD = 0.45

// Tracking constants
const IOA_THRESHOLD = 0.8 // Intersection over Area for tracking
let nextPlayerId = 1

// Helper function to calculate Intersection over Union
function calculateIou(box1: BoundingBox, box2: BoundingBox): number {
  const [x1, y1, w1, h1] = box1
  const [x2, y2, w2, h2] = box2

  const x_left = Math.max(x1, x2)
  const y_top = Math.max(y1, y2)
  const x_right = Math.min(x1 + w1, x2 + w2)
  const y_bottom = Math.min(y1 + h1, y2 + h2)

  if (x_right < x_left || y_bottom < y_top) {
    return 0.0
  }

  const intersection_area = (x_right - x_left) * (y_bottom - y_top)
  const box1_area = w1 * h1
  const box2_area = w2 * h2
  const union_area = box1_area + box2_area - intersection_area

  return intersection_area / union_area
}

export default function Home() {
  const { toast } = useToast()

  // Video and file state
  const [videoFile, setVideoFile] = React.useState<File | null>(null)
  const [videoUrl, setVideoUrl] = React.useState<string | null>(null)
  const [isDragOver, setIsDragOver] = React.useState(false)

  // Model and session state
  const [session, setSession] = React.useState<ort.InferenceSession | null>(null)
  const [modelLoading, setModelLoading] = React.useState(false)
  const [modelLoadingError, setModelLoadingError] = React.useState<string | null>(null)

  // Tracking and processing state
  const [isTracking, setIsTracking] = React.useState(false)
  const [trackingData, setTrackingData] = React.useState<TrackingData | null>(null)
  const [fullTrackingData, setFullTrackingData] = React.useState<TrackingData>([])
  const [trackedPlayers, setTrackedPlayers] = React.useState<Map<string, Player>>(new Map())
  const [currentTime, setCurrentTime] = React.useState(0)
  const [isSummarizing, setIsSummarizing] = React.useState(false)
  const [summary, setSummary] = React.useState<string | null>(null)

  // Refs
  const videoRef = React.useRef<HTMLVideoElement>(null)
  const canvasRef = React.useRef<HTMLCanvasElement>(null)
  const fileInputRef = React.useRef<HTMLInputElement>(null)
  const animationFrameId = React.useRef<number>()

  // Load the ONNX model
  React.useEffect(() => {
    async function loadModel() {
      try {
        setModelLoading(true)
        setModelLoadingError(null)

        // Configure ONNX Runtime to use local WASM files
        ort.env.wasm.wasmPaths = '/onnx-wasm/'
        ort.env.wasm.numThreads = 1
        ort.env.wasm.simd = true

        // Try different execution providers in order of preference
        const executionProviders = ['wasm']

        const newSession = await ort.InferenceSession.create(MODEL_URL, {
          executionProviders,
        })
        setSession(newSession)
        toast({
          title: 'Model Loaded',
          description: 'The player detection model is ready.',
        })
      } catch (error) {
        console.error('Failed to load ONNX model:', error)
        setModelLoadingError('Could not load model. Please ensure `model.onnx` is in the /public directory.')
        toast({
          variant: 'destructive',
          title: 'Model Loading Failed',
          description: 'Could not load `model.onnx`. Make sure it is present in the /public directory.',
        })
      } finally {
        setModelLoading(false)
      }
    }
    loadModel()
  }, [toast])

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      processVideoFile(file)
    }
  }

  const processVideoFile = (file: File) => {
    // Validate file type
    if (!file.type.startsWith('video/')) {
      toast({
        variant: 'destructive',
        title: 'Invalid File Type',
        description: 'Please select a video file.',
      })
      return
    }

    // Validate file size (100MB limit)
    const maxSize = 100 * 1024 * 1024 // 100MB
    if (file.size > maxSize) {
      toast({
        variant: 'destructive',
        title: 'File Too Large',
        description: 'Please select a video file smaller than 100MB.',
      })
      return
    }

    if (isTracking) {
      handleToggleTracking()
    }

    setVideoFile(file)
    const url = URL.createObjectURL(file)
    setVideoUrl(url)
    setTrackingData(null)
    setFullTrackingData([])
    setSummary(null)
    setTrackedPlayers(new Map())
    nextPlayerId = 1
    setCurrentTime(0)

    toast({
      title: 'Video Loaded',
      description: `Successfully loaded ${file.name}`,
    })
  }

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault()
    event.stopPropagation()
    setIsDragOver(true)
  }

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault()
    event.stopPropagation()
    setIsDragOver(false)
  }

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault()
    event.stopPropagation()
    setIsDragOver(false)

    const files = event.dataTransfer.files
    if (files.length > 0) {
      const file = files[0]
      processVideoFile(file)
    }
  }

  const detectObjectsOnFrame = React.useCallback(
    async (
      videoElement: HTMLVideoElement
    ): Promise<Player[]> => {
      if (!session) return []

      const [modelWidth, modelHeight] = MODEL_INPUT_SHAPE.slice(2)
      const canvas = canvasRef.current
      if (!canvas) return []
      const ctx = canvas.getContext('2d')
      if (!ctx) return []

      canvas.width = modelWidth
      canvas.height = modelHeight
      ctx.drawImage(videoElement, 0, 0, modelWidth, modelHeight)
      const imageData = ctx.getImageData(0, 0, modelWidth, modelHeight)
      const data = imageData.data

      const red = [], green = [], blue = []
      for (let i = 0; i < data.length; i += 4) {
          red.push(data[i] / 255)
          green.push(data[i + 1] / 255)
          blue.push(data[i + 2] / 255)
      }
      const transposed = [...red, ...green, ...blue]
      const inputTensor = new ort.Tensor('float32', transposed, MODEL_INPUT_SHAPE)

      const outputs = await session.run({ images: inputTensor })
      const outputTensor = outputs[Object.keys(outputs)[0]]

      const boxes: BoundingBox[] = []
      const classIds: number[] = []
      const scores: number[] = []

      for (let i = 0; i < outputTensor.dims[2]; i++) {
        const data = outputTensor.data.slice(i * outputTensor.dims[1], (i + 1) * outputTensor.dims[1])
        const [x_center, y_center, width, height, ...classProbs] = data

        let maxProb = 0
        let maxIndex = -1
        classProbs.forEach((prob, j) => {
            const probNum = Number(prob)
            if (probNum > maxProb) {
                maxProb = probNum
                maxIndex = j
            }
        });

        if (maxProb > CONFIDENCE_THRESHOLD && (maxIndex === 0 /* person */ || maxIndex === 32 /* sports ball */)) {
          const x = (Number(x_center) - Number(width) / 2) / modelWidth * videoElement.videoWidth
          const y = (Number(y_center) - Number(height) / 2) / modelHeight * videoElement.videoHeight
          const w = Number(width) / modelWidth * videoElement.videoWidth
          const h = Number(height) / modelHeight * videoElement.videoHeight
          
          boxes.push([x, y, w, h])
          classIds.push(maxIndex)
          scores.push(maxProb)
        }
      }

      const nmsIndices = nonMaxSuppression(boxes, scores, IOU_THRESHOLD)

      return nmsIndices.map(index => ({
          id: `detection_${index}`, // temporary ID
          box: boxes[index],
          confidence: scores[index]
      }));
    },
    [session]
  )

  const nonMaxSuppression = (boxes: BoundingBox[], scores: number[], iouThreshold: number): number[] => {
    const indices = scores.map((_, i) => i);
    indices.sort((a, b) => scores[b] - scores[a]);

    const keep: number[] = [];
    while (indices.length > 0) {
        const i = indices.shift();
        if (i === undefined) continue;
        keep.push(i);
        for (let j = indices.length - 1; j >= 0; j--) {
            const k = indices[j];
            const iou = calculateIou(boxes[i], boxes[k]);
            if (iou > iouThreshold) {
                indices.splice(j, 1);
            }
        }
    }
    return keep;
  };

  const updateTracking = React.useCallback( (detections: Player[]) => {
      const newTrackedPlayers = new Map<string, Player>(trackedPlayers);
      const matchedDetections = new Set<number>();
      
      // Try to match existing players
      for (const [id, player] of newTrackedPlayers.entries()) {
          let bestMatchIndex = -1;
          let bestIou = 0;
          detections.forEach((detection, index) => {
              const iou = calculateIou(player.box, detection.box);
              if (iou > bestIou && iou > IOA_THRESHOLD) {
                  bestIou = iou;
                  bestMatchIndex = index;
              }
          });

          if (bestMatchIndex !== -1) {
              newTrackedPlayers.set(id, { ...detections[bestMatchIndex], id });
              matchedDetections.add(bestMatchIndex);
          } else {
              // Player might be lost, for now we just keep them
          }
      }

      // Add new players for unmatched detections
      detections.forEach((detection, index) => {
          if (!matchedDetections.has(index)) {
              const newId = `player_${nextPlayerId++}`;
              newTrackedPlayers.set(newId, { ...detection, id: newId });
          }
      });
      
      setTrackedPlayers(newTrackedPlayers);
      return Array.from(newTrackedPlayers.values());
  }, [trackedPlayers]);


  const processingLoop = React.useCallback(async () => {
    if (!isTracking || !videoRef.current || videoRef.current.paused || videoRef.current.ended) {
      setIsTracking(false)
      return
    }

    const detections = await detectObjectsOnFrame(videoRef.current)
    const players = updateTracking(detections);
    const newFrameData = { frame: Math.floor(videoRef.current!.currentTime * FPS), players };

    setTrackingData([newFrameData]);
    setFullTrackingData(prev => [...prev, newFrameData]);

    animationFrameId.current = requestAnimationFrame(processingLoop)
  }, [isTracking, detectObjectsOnFrame, updateTracking])

  const handleToggleTracking = () => {
    setIsTracking(prev => {
      const newIsTracking = !prev
      if (newIsTracking) {
        setSummary(null)
        toast({
          title: 'Tracking started',
          description: 'AI is analyzing the video frame by frame.',
        })
        videoRef.current?.play()
        animationFrameId.current = requestAnimationFrame(processingLoop)
      } else {
        toast({
          title: 'Tracking stopped',
        })
        videoRef.current?.pause()
        if (animationFrameId.current) {
          cancelAnimationFrame(animationFrameId.current)
        }
      }
      return newIsTracking
    })
  }
  
  const handleGenerateSummary = async () => {
    if (!fullTrackingData || fullTrackingData.length === 0 || !videoRef.current) return

    setIsSummarizing(true)
    setSummary(null)
    toast({ title: 'Generating summary...', description: 'The AI is analyzing the full game.' })
    try {
      const result = await summarizeActivity({
        trackingDataJson: JSON.stringify(fullTrackingData),
        videoDuration: videoRef.current.duration,
      })
      setSummary(result.summary)
      toast({ title: 'Summary generated!' })
    } catch (error) {
      console.error('Failed to generate summary:', error)
      toast({
        variant: 'destructive',
        title: 'Summary Failed',
        description: 'Could not generate the activity summary.',
      })
    } finally {
      setIsSummarizing(false)
    }
  }

  const currentFrameData: FrameData | null = trackingData ? trackingData[0] : null;
  const totalPlayers = trackedPlayers.size;
  const activePlayers = currentFrameData?.players.length ?? 0

  return (
    <SidebarProvider>
      <Sidebar collapsible="icon" className="border-r bg-card text-card-foreground">
        <SidebarHeader>
          <FieldIdLogo />
        </SidebarHeader>
        <SidebarContent className="p-4 space-y-6">
          <div>
            <Label htmlFor="video-upload" className="mb-2 block">
              Upload Video
            </Label>
            <div
              className={cn(
                "relative border-2 border-dashed rounded-lg p-6 transition-colors cursor-pointer",
                isDragOver
                  ? "border-primary bg-primary/5"
                  : "border-muted-foreground/25 hover:border-muted-foreground/50",
                "group"
              )}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              <div className="flex flex-col items-center justify-center space-y-2 text-center">
                <UploadCloud className={cn(
                  "h-8 w-8 transition-colors",
                  isDragOver ? "text-primary" : "text-muted-foreground group-hover:text-foreground"
                )} />
                <div className="space-y-1">
                  <p className={cn(
                    "text-sm font-medium transition-colors",
                    isDragOver ? "text-primary" : "text-foreground"
                  )}>
                    {isDragOver ? "Drop video here" : "Click to upload or drag and drop"}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    MP4, AVI, MOV, WebM (max 100MB)
                  </p>
                </div>
              </div>
              <Input
                id="video-upload"
                ref={fileInputRef}
                type="file"
                accept="video/*"
                className="hidden"
                onChange={handleFileChange}
              />
            </div>
            {videoFile && (
              <div className="mt-3 p-3 bg-muted/50 rounded-md">
                <div className="flex items-center gap-2">
                  <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                  <p className="text-sm font-medium truncate">
                    {videoFile.name}
                  </p>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {(videoFile.size / (1024 * 1024)).toFixed(1)} MB
                </p>
              </div>
            )}
          </div>
          
          <canvas ref={canvasRef} className="hidden" />

          <div className="space-y-3">
            <Button
              onClick={handleToggleTracking}
              disabled={!videoFile || modelLoading || !!modelLoadingError}
              className="w-full"
              size="lg"
            >
              {modelLoading ? (
                <Loader className="mr-2 h-4 w-4 animate-spin" />
              ) : isTracking ? (
                <StopCircle className="mr-2 h-4 w-4" />
              ) : (
                <Play className="mr-2 h-4 w-4" />
              )}
              {modelLoading ? 'Loading Model...' : isTracking ? 'Stop Tracking' : 'Start Tracking'}
            </Button>

            {modelLoading && (
              <div className="space-y-2">
                <Progress value={undefined} className="w-full" />
                <p className="text-xs text-center text-muted-foreground">
                  Initializing AI model...
                </p>
              </div>
            )}

            {modelLoadingError && (
              <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-md">
                <p className="text-sm text-destructive font-medium">Model Loading Failed</p>
                <p className="text-xs text-destructive/80 mt-1">{modelLoadingError}</p>
              </div>
            )}
          </div>

          {(isTracking || trackingData) && (
            <Card className="bg-gradient-to-br from-background to-muted/20">
              <CardHeader className="pb-3">
                <h3 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">
                  Live Statistics
                </h3>
              </CardHeader>
              <CardContent className="space-y-4">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center justify-between p-3 bg-background/50 rounded-lg border">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-blue-500/10 rounded-full">
                            <Users className="h-4 w-4 text-blue-500" />
                          </div>
                          <span className="font-medium">Total Players</span>
                        </div>
                        <span className="font-bold text-xl">{totalPlayers}</span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Total unique players detected in the video</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center justify-between p-3 bg-background/50 rounded-lg border">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-green-500/10 rounded-full">
                            <UserCheck className="h-4 w-4 text-green-500" />
                          </div>
                          <span className="font-medium">Active</span>
                        </div>
                        <span className="font-bold text-xl text-green-500">{activePlayers}</span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Players currently visible in the frame</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center justify-between p-3 bg-background/50 rounded-lg border">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-orange-500/10 rounded-full">
                            <UserX className="h-4 w-4 text-orange-500" />
                          </div>
                          <span className="font-medium">Inactive</span>
                        </div>
                        <span className="font-bold text-xl text-orange-500">
                          {totalPlayers > activePlayers ? totalPlayers - activePlayers : 0}
                        </span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Players not currently visible but still being tracked</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </CardContent>
            </Card>
          )}

          <div className="space-y-3">
            <Button
              onClick={handleGenerateSummary}
              disabled={isTracking || fullTrackingData.length === 0 || isSummarizing || !videoFile}
              className="w-full"
              variant={summary ? "outline" : "default"}
            >
              {isSummarizing ? (
                <Loader className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <MessageSquareQuote className="mr-2 h-4 w-4" />
              )}
              {isSummarizing ? 'Analyzing Activity...' : summary ? 'Regenerate Summary' : 'Generate AI Summary'}
            </Button>

            {isSummarizing && (
              <div className="space-y-2">
                <Progress value={undefined} className="w-full" />
                <p className="text-xs text-center text-muted-foreground">
                  AI is analyzing player movements and game events...
                </p>
              </div>
            )}
          </div>

          {summary && (
            <Card className="bg-gradient-to-br from-blue-50/50 to-indigo-50/50 dark:from-blue-950/20 dark:to-indigo-950/20 border-blue-200/50 dark:border-blue-800/50">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-2">
                  <div className="p-2 bg-blue-500/10 rounded-full">
                    <MessageSquareQuote className="h-4 w-4 text-blue-500" />
                  </div>
                  <span className="font-semibold">AI Activity Summary</span>
                </div>
              </CardHeader>
              <CardContent>
                <div className="prose prose-sm dark:prose-invert max-w-none">
                  <p className="text-sm leading-relaxed">{summary}</p>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="flex-grow" />

          <div className="space-y-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="secondary"
                    className="w-full"
                    disabled={!trackingData || fullTrackingData.length === 0}
                    onClick={() => {
                      if (fullTrackingData.length > 0) {
                        const dataStr = JSON.stringify(fullTrackingData, null, 2)
                        const dataBlob = new Blob([dataStr], { type: 'application/json' })
                        const url = URL.createObjectURL(dataBlob)
                        const link = document.createElement('a')
                        link.href = url
                        link.download = `field-id-tracking-${new Date().toISOString().split('T')[0]}.json`
                        link.click()
                        URL.revokeObjectURL(url)
                        toast({
                          title: 'Download Complete',
                          description: 'Tracking data has been downloaded successfully.',
                        })
                      }
                    }}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Download Tracking Data
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Download player tracking data as JSON file</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {trackingData && fullTrackingData.length > 0 && (
              <p className="text-xs text-center text-muted-foreground">
                {fullTrackingData.length} frames of tracking data available
              </p>
            )}
          </div>
        </SidebarContent>
      </Sidebar>
      <SidebarInset className="p-4 bg-background">
        <header className="flex items-center justify-between gap-4 mb-6">
          <div className="flex items-center gap-4">
            <SidebarTrigger className="md:hidden" />
            <div>
              <h2 className="text-2xl font-bold">Video Analysis</h2>
              <p className="text-sm text-muted-foreground">
                AI-powered sports player tracking and identification
              </p>
            </div>
          </div>
          <DarkModeToggle />
        </header>
        <main>
          <Card className="h-full">
            <CardContent className="p-2 md:p-6 h-full">
              {videoUrl ? (
                <VideoPlayer
                  videoRef={videoRef}
                  videoUrl={videoUrl}
                  onTimeUpdate={e => setCurrentTime(e.currentTarget.currentTime)}
                  currentFrameData={currentFrameData}
                />
              ) : (
                <div className="flex flex-col items-center justify-center h-[70vh] border-2 border-dashed rounded-lg bg-gradient-to-br from-muted/20 to-muted/5">
                  <div className="text-center space-y-4 max-w-md">
                    <div className="p-4 bg-primary/10 rounded-full w-fit mx-auto">
                      <UploadCloud className="h-12 w-12 text-primary" />
                    </div>
                    <div className="space-y-2">
                      <h3 className="text-xl font-semibold">Ready for Video Analysis</h3>
                      <p className="text-muted-foreground">
                        Upload a sports video to start AI-powered player tracking and identification
                      </p>
                    </div>
                    <div className="flex flex-wrap justify-center gap-2 text-xs text-muted-foreground">
                      <span className="px-2 py-1 bg-muted rounded">MP4</span>
                      <span className="px-2 py-1 bg-muted rounded">AVI</span>
                      <span className="px-2 py-1 bg-muted rounded">MOV</span>
                      <span className="px-2 py-1 bg-muted rounded">WebM</span>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </main>
      </SidebarInset>
    </SidebarProvider>
  )
}

'use client'

import * as React from 'react'
import type { BoundingBox, FrameData } from '@/lib/types'

interface VideoPlayerProps {
  videoRef: React.RefObject<HTMLVideoElement>
  videoUrl: string
  onTimeUpdate: (event: React.SyntheticEvent<HTMLVideoElement, Event>) => void
  currentFrameData: FrameData | null
}

export function VideoPlayer({
  videoRef,
  videoUrl,
  onTimeUpdate,
  currentFrameData,
}: VideoPlayerProps) {
  const containerRef = React.useRef<HTMLDivElement>(null)
  const [videoDimensions, setVideoDimensions] = React.useState({
    width: 0,
    height: 0,
  })
  const [containerDimensions, setContainerDimensions] = React.useState({
    width: 0,
    height: 0,
  })

  React.useEffect(() => {
    const video = videoRef.current
    if (video) {
      const handleLoadedMetadata = () => {
        setVideoDimensions({ width: video.videoWidth, height: video.videoHeight })
      }
      video.addEventListener('loadedmetadata', handleLoadedMetadata)
      return () => video.removeEventListener('loadedmetadata', handleLoadedMetadata)
    }
  }, [videoRef])

  React.useEffect(() => {
    const container = containerRef.current
    if (container) {
      const resizeObserver = new ResizeObserver(entries => {
        for (const entry of entries) {
          setContainerDimensions({
            width: entry.contentRect.width,
            height: entry.contentRect.height,
          })
        }
      })
      resizeObserver.observe(container)
      return () => resizeObserver.disconnect()
    }
  }, [])

  const scaleAndPositionBox = (box: BoundingBox): BoundingBox => {
    const { width: videoWidth, height: videoHeight } = videoDimensions
    const { width: containerWidth, height: containerHeight } = containerDimensions

    if (!videoWidth || !videoHeight || !containerWidth || !containerHeight) {
      return [0, 0, 0, 0]
    }

    const videoAspectRatio = videoWidth / videoHeight
    const containerAspectRatio = containerWidth / containerHeight

    let renderWidth, renderHeight, offsetX, offsetY

    if (videoAspectRatio > containerAspectRatio) {
      renderWidth = containerWidth
      renderHeight = containerWidth / videoAspectRatio
      offsetX = 0
      offsetY = (containerHeight - renderHeight) / 2
    } else {
      renderHeight = containerHeight
      renderWidth = containerHeight * videoAspectRatio
      offsetX = (containerWidth - renderWidth) / 2
      offsetY = 0
    }

    const scale = renderWidth / videoWidth
    const [x, y, w, h] = box
    return [x * scale + offsetX, y * scale + offsetY, w * scale, h * scale]
  }

  return (
    <div ref={containerRef} className="relative w-full h-full max-h-[75vh] bg-black rounded-md overflow-hidden">
      <video
        ref={videoRef}
        src={videoUrl}
        controls
        onTimeUpdate={onTimeUpdate}
        onLoadedMetadata={e => {
          setVideoDimensions({
            width: e.currentTarget.videoWidth,
            height: e.currentTarget.videoHeight,
          })
        }}
        className="w-full h-full object-contain"
      />
      {currentFrameData && (
        <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
          {currentFrameData.players.map(player => {
            const [x, y, w, h] = scaleAndPositionBox(player.box)
            return (
              <div
                key={player.id}
                className="absolute border-2 border-primary rounded-sm transition-all duration-100"
                style={{
                  left: `${x}px`,
                  top: `${y}px`,
                  width: `${w}px`,
                  height: `${h}px`,
                }}
              >
                <span className="absolute -top-6 left-0 bg-primary text-primary-foreground text-xs font-bold px-2 py-0.5 rounded-sm whitespace-nowrap">
                  {player.id.replace('_', ' ').toUpperCase()}
                </span>
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}

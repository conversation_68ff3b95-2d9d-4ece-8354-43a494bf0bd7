#!/usr/bin/env node

/**
 * Security check script to prevent committing sensitive data
 * Run this before committing to ensure no API keys or secrets are included
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Patterns to look for (case insensitive)
const SENSITIVE_PATTERNS = [
  /AIzaSy[A-Za-z0-9_-]{33}/g, // Google API Key pattern
  /sk-[A-Za-z0-9]{48}/g, // OpenAI API Key pattern
  /xoxb-[0-9]{11}-[0-9]{11}-[A-Za-z0-9]{24}/g, // Slack Bot Token
  /ghp_[A-Za-z0-9]{36}/g, // GitHub Personal Access Token
  /AKIA[0-9A-Z]{16}/g, // AWS Access Key
  /password\s*[:=]\s*['"]\w+['"]/gi,
  /secret\s*[:=]\s*['"]\w+['"]/gi,
  /token\s*[:=]\s*['"]\w+['"]/gi,
  /api[_-]?key\s*[:=]\s*['"]\w+['"]/gi,
];

function checkFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const violations = [];
    
    SENSITIVE_PATTERNS.forEach((pattern, index) => {
      const matches = content.match(pattern);
      if (matches) {
        violations.push({
          pattern: pattern.toString(),
          matches: matches,
          file: filePath
        });
      }
    });
    
    return violations;
  } catch (error) {
    // File might be binary or inaccessible
    return [];
  }
}

function main() {
  console.log('🔍 Running security check...');
  
  try {
    // Get list of staged files
    const stagedFiles = execSync('git diff --cached --name-only', { encoding: 'utf8' })
      .split('\n')
      .filter(file => file.trim() !== '');
    
    if (stagedFiles.length === 0) {
      console.log('ℹ️  No staged files to check');
      return;
    }
    
    console.log(`📁 Checking ${stagedFiles.length} staged files...`);
    
    let totalViolations = 0;
    
    stagedFiles.forEach(file => {
      if (fs.existsSync(file)) {
        const violations = checkFile(file);
        if (violations.length > 0) {
          console.log(`\n❌ SECURITY VIOLATION in ${file}:`);
          violations.forEach(violation => {
            console.log(`   Pattern: ${violation.pattern}`);
            console.log(`   Matches: ${violation.matches.join(', ')}`);
          });
          totalViolations += violations.length;
        }
      }
    });
    
    if (totalViolations > 0) {
      console.log(`\n🚨 SECURITY CHECK FAILED!`);
      console.log(`Found ${totalViolations} potential security violations.`);
      console.log(`Please remove sensitive data before committing.`);
      process.exit(1);
    } else {
      console.log('\n✅ Security check passed! No sensitive data detected.');
    }
    
  } catch (error) {
    console.error('Error running security check:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { checkFile, SENSITIVE_PATTERNS };

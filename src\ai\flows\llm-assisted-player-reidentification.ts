'use server';
/**
 * @fileOverview This file defines a Genkit flow for LLM-assisted player re-identification.
 *
 * - llmAssistedPlayerReidentification - A function that handles the player re-identification process.
 * - LLMAssistedPlayerReidentificationInput - The input type for the llmAssistedPlayerReidentification function.
 * - LLMAssistedPlayerReidentificationOutput - The return type for the llmAssistedPlayerReidentification function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const LLMAssistedPlayerReidentificationInputSchema = z.object({
  playerDescription: z
    .string()
    .describe('The description of the player, including appearance and location.'),
  currentFrameData: z.string().describe('Data about the current frame, including detected players.'),
  previousPlayerId: z.string().describe('The ID of the player before they left the frame.'),
});
export type LLMAssistedPlayerReidentificationInput = z.infer<
  typeof LLMAssistedPlayerReidentificationInputSchema
>;

const LLMAssistedPlayerReidentificationOutputSchema = z.object({
  reidentifiedPlayerId: z
    .string()
    .describe('The ID of the re-identified player, or null if not found.'),
  reasoning: z.string().describe('The reasoning behind the re-identification decision.'),
});
export type LLMAssistedPlayerReidentificationOutput = z.infer<
  typeof LLMAssistedPlayerReidentificationOutputSchema
>;

export async function llmAssistedPlayerReidentification(
  input: LLMAssistedPlayerReidentificationInput
): Promise<LLMAssistedPlayerReidentificationOutput> {
  return llmAssistedPlayerReidentificationFlow(input);
}

const prompt = ai.definePrompt({
  name: 'llmAssistedPlayerReidentificationPrompt',
  input: {schema: LLMAssistedPlayerReidentificationInputSchema},
  output: {schema: LLMAssistedPlayerReidentificationOutputSchema},
  prompt: `You are an expert in video analysis and player tracking.

A player with the following description left the frame:
{{{playerDescription}}}

Now, a new frame has the following player data:
{{{currentFrameData}}}

Given that the original player had ID {{{previousPlayerId}}}, determine if any of the players in the current frame are the same player. If so, return their ID and explain your reasoning. If not, return null for the ID.

Return your answer in JSON format.
`,
});

const llmAssistedPlayerReidentificationFlow = ai.defineFlow(
  {
    name: 'llmAssistedPlayerReidentificationFlow',
    inputSchema: LLMAssistedPlayerReidentificationInputSchema,
    outputSchema: LLMAssistedPlayerReidentificationOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);

# Field ID Deployment Guide

This guide provides detailed instructions for deploying Field ID to various platforms.

## Quick Start

1. **Prepare your repository**:
   ```bash
   git clone https://github.com/your-username/field-id.git
   cd field-id
   npm install
   ```

2. **Set up environment variables**:
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your API keys
   ```

3. **Test locally**:
   ```bash
   npm run build
   npm start
   ```

## Platform-Specific Deployment

### Vercel (Recommended)

**Why Vercel?**
- Optimized for Next.js applications
- Automatic deployments from Git
- Built-in CDN and edge functions
- Excellent performance for ONNX/WASM files

**Deployment Steps:**

1. **Via Vercel Dashboard**:
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your GitHub repository
   - Configure build settings (auto-detected)
   - Add environment variables:
     - `GOOGLE_GENAI_API_KEY`
   - Deploy

2. **Via CLI**:
   ```bash
   npm install -g vercel
   vercel login
   vercel --prod
   ```

3. **GitHub Integration**:
   - Install Vercel GitHub app
   - Automatic deployments on push
   - Preview deployments for PRs

### Netlify

**Deployment Steps:**

1. **Via Netlify Dashboard**:
   - Go to [netlify.com](https://netlify.com)
   - Click "New site from Git"
   - Connect your repository
   - Build settings:
     - Build command: `npm run build`
     - Publish directory: `.next`
   - Environment variables:
     - `GOOGLE_GENAI_API_KEY`

2. **Via CLI**:
   ```bash
   npm install -g netlify-cli
   netlify login
   netlify deploy --prod
   ```

### Railway

**Deployment Steps:**

1. **Via Railway Dashboard**:
   - Go to [railway.app](https://railway.app)
   - Create new project from GitHub
   - Railway auto-detects Next.js
   - Add environment variables
   - Deploy

### DigitalOcean App Platform

**Deployment Steps:**

1. **Via DigitalOcean Dashboard**:
   - Go to [cloud.digitalocean.com](https://cloud.digitalocean.com)
   - Create new app from GitHub
   - Configure build settings
   - Add environment variables
   - Deploy

## Model File Deployment Strategies

### Strategy 1: Include in Repository (Simple)

**Pros:**
- Simple setup
- No external dependencies
- Works out of the box

**Cons:**
- Large repository size (389MB)
- Slower cloning and deployments
- Git storage costs

**Implementation:**
- Keep `model.onnx` in `public/` directory
- Ensure Git LFS is configured for large files

### Strategy 2: External CDN Hosting (Recommended)

**Pros:**
- Faster deployments
- Better performance
- Reduced repository size
- Scalable

**Cons:**
- Additional setup required
- External dependency

**Implementation:**

1. **Upload to CDN**:
   ```bash
   # AWS S3 example
   aws s3 cp public/model.onnx s3://your-bucket/model.onnx --acl public-read
   ```

2. **Set environment variable**:
   ```env
   NEXT_PUBLIC_MODEL_URL=https://your-cdn.com/model.onnx
   ```

3. **Configure CORS** (if needed):
   ```json
   {
     "CORSRules": [
       {
         "AllowedOrigins": ["https://your-domain.com"],
         "AllowedMethods": ["GET"],
         "AllowedHeaders": ["*"]
       }
     ]
   }
   ```

### Strategy 3: Git LFS (Balanced)

**Setup:**
```bash
git lfs install
git lfs track "*.onnx"
git add .gitattributes
git add public/model.onnx
git commit -m "Add model with Git LFS"
```

## Environment Variables

### Required Variables

```env
# Google AI API Key (required for AI features)
GOOGLE_GENAI_API_KEY=your_api_key_here
```

### Optional Variables

```env
# Custom model URL (if hosting externally)
NEXT_PUBLIC_MODEL_URL=https://cdn.example.com/model.onnx

# Analytics
NEXT_PUBLIC_ANALYTICS_ID=your_analytics_id

# Error monitoring
SENTRY_DSN=your_sentry_dsn

# Environment
NODE_ENV=production
```

## Performance Optimization

### 1. Model Loading Optimization

```javascript
// Lazy load model only when needed
const loadModel = async () => {
  const modelUrl = process.env.NEXT_PUBLIC_MODEL_URL || '/model.onnx';
  return await ort.InferenceSession.create(modelUrl);
};
```

### 2. Bundle Optimization

```bash
# Analyze bundle size
npm run build:analyze

# Check for unused dependencies
npx depcheck
```

### 3. Caching Strategy

Configure proper cache headers:
- Model files: `Cache-Control: public, max-age=31536000, immutable`
- WASM files: `Cache-Control: public, max-age=31536000, immutable`

## Security Considerations

### 1. Environment Variables

- Never commit API keys to repository
- Use platform-specific secret management
- Rotate keys regularly

### 2. CORS Configuration

```javascript
// next.config.ts
headers: [
  {
    source: '/api/:path*',
    headers: [
      { key: 'Access-Control-Allow-Origin', value: 'https://your-domain.com' },
      { key: 'Access-Control-Allow-Methods', value: 'GET, POST, OPTIONS' },
    ],
  },
]
```

### 3. Content Security Policy

```javascript
// next.config.ts
headers: [
  {
    source: '/(.*)',
    headers: [
      {
        key: 'Content-Security-Policy',
        value: "default-src 'self'; script-src 'self' 'unsafe-eval'; worker-src 'self' blob:;"
      },
    ],
  },
]
```

## Monitoring and Debugging

### 1. Error Tracking

```bash
npm install @sentry/nextjs
```

### 2. Performance Monitoring

```bash
npm install @vercel/analytics
```

### 3. Logging

```javascript
// Use structured logging
console.log(JSON.stringify({
  level: 'info',
  message: 'Model loaded successfully',
  timestamp: new Date().toISOString(),
  modelSize: modelSizeInMB
}));
```

## Troubleshooting

### Common Issues

1. **Build Failures**:
   - Check Node.js version (>=18)
   - Verify environment variables
   - Clear cache: `rm -rf .next node_modules && npm install`

2. **Model Loading Errors**:
   - Verify model file accessibility
   - Check HTTPS requirement for WASM
   - Confirm CORS headers

3. **Performance Issues**:
   - Use external model hosting
   - Enable compression
   - Optimize images and videos

### Debug Commands

```bash
# Check build output
npm run build

# Test production build locally
npm run start

# Analyze bundle
npm run build:analyze

# Check dependencies
npm audit
```

## Deployment Checklist

- [ ] Repository prepared and cleaned
- [ ] Environment variables configured
- [ ] Model file strategy chosen and implemented
- [ ] Build process tested locally
- [ ] HTTPS enabled on deployment platform
- [ ] CORS configured (if using external model)
- [ ] Error monitoring set up
- [ ] Performance monitoring configured
- [ ] Security headers configured
- [ ] Cache headers optimized
- [ ] Domain configured (if custom)
- [ ] SSL certificate verified
- [ ] Backup strategy in place

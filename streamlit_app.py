"""
Field ID - AI-Powered Sports Player Tracking
Advanced Streamlit Dashboard (Optimized & Lightweight)

A sophisticated, modern dashboard for AI-powered sports player tracking
with advanced analytics, interactive visualizations, and professional UI.
Uses simulation-based approach for demonstration without heavy AI dependencies.
"""

import streamlit as st
import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import tempfile
import os
import json
from datetime import datetime, timedelta
import time
import uuid
import random
from typing import Dict, List, Optional, Tuple

# Configure Streamlit page
st.set_page_config(
    page_title="Field ID - Advanced Sports Analytics",
    page_icon="⚽",
    layout="wide",
    initial_sidebar_state="expanded"
)

# ============================================================================
# MOCK AI SYSTEM - Simulation-based approach for demonstration
# ============================================================================

class MockVideoProcessor:
    """Mock video processor that simulates AI-powered player tracking"""

    def __init__(self):
        self.is_processing = False
        self.current_frame = 0
        self.total_frames = 0

    def get_video_info(self, file_path: str) -> Dict:
        """Simulate video metadata extraction"""
        try:
            # Simulate video analysis without actually opening the file
            file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0

            # Generate realistic mock metadata
            mock_metadata = {
                'fps': random.uniform(24, 60),
                'frame_count': random.randint(1000, 10000),
                'duration': random.uniform(30, 300),  # 30 seconds to 5 minutes
                'width': random.choice([1280, 1920, 854, 1024]),
                'height': random.choice([720, 1080, 480, 576]),
                'file_size': file_size
            }

            mock_metadata['resolution'] = f"{mock_metadata['width']}x{mock_metadata['height']}"
            return mock_metadata

        except Exception as e:
            st.error(f"Error processing video metadata: {str(e)}")
            return self._get_default_metadata()

    def _get_default_metadata(self) -> Dict:
        """Return default metadata if video processing fails"""
        return {
            'fps': 30.0,
            'frame_count': 3000,
            'duration': 100.0,
            'width': 1280,
            'height': 720,
            'resolution': "1280x720",
            'file_size': 0
        }

    def simulate_player_detection(self, frame_number: int) -> Dict:
        """Simulate AI player detection for a given frame"""
        # Simulate realistic player detection patterns
        base_players = 11  # Standard soccer team
        variation = random.randint(-2, 3)
        total_players = max(6, base_players + variation)

        # Simulate some players being temporarily occluded
        visible_players = max(4, total_players - random.randint(0, 3))

        # Generate player positions (normalized coordinates 0-100)
        players = []
        for i in range(visible_players):
            player = {
                'id': f'player_{i+1}',
                'x': random.uniform(10, 90),  # Field width
                'y': random.uniform(15, 85),  # Field height
                'confidence': random.uniform(0.7, 0.98),
                'speed': random.uniform(0, 8.5),  # m/s
                'direction': random.uniform(0, 360)  # degrees
            }
            players.append(player)

        return {
            'frame_number': frame_number,
            'timestamp': frame_number / 30.0,  # Assume 30 FPS
            'total_players': total_players,
            'visible_players': visible_players,
            'players': players,
            'detection_confidence': random.uniform(0.85, 0.98)
        }

class MockAIAnalyzer:
    """Mock AI analyzer that generates realistic sports analytics"""

    def __init__(self):
        self.analysis_cache = {}

    def analyze_game_flow(self, tracking_data: List[Dict]) -> Dict:
        """Simulate advanced game flow analysis"""
        if not tracking_data:
            return self._get_default_analysis()

        # Simulate tactical analysis
        formations = ['4-4-2', '4-3-3', '3-5-2', '4-2-3-1']
        current_formation = random.choice(formations)

        # Calculate mock statistics
        avg_players = np.mean([d.get('visible_players', 0) for d in tracking_data])
        max_intensity = max([d.get('detection_confidence', 0) for d in tracking_data])

        analysis = {
            'formation': current_formation,
            'formation_stability': random.uniform(0.75, 0.95),
            'average_players': avg_players,
            'peak_intensity': max_intensity,
            'tactical_switches': random.randint(2, 8),
            'field_coverage': random.uniform(0.65, 0.90),
            'coordination_score': random.uniform(0.70, 0.95),
            'pressure_phases': random.randint(8, 15),
            'transition_efficiency': random.uniform(0.80, 0.95)
        }

        return analysis

    def generate_insights(self, analysis_data: Dict) -> List[Dict]:
        """Generate AI-powered insights and recommendations"""
        insights = [
            {
                'type': 'tactical',
                'title': 'Formation Analysis',
                'description': f"Team maintained {analysis_data.get('formation', '4-3-3')} formation with {analysis_data.get('formation_stability', 0.85)*100:.1f}% stability",
                'priority': 'high',
                'impact': 'Defensive Structure +12%'
            },
            {
                'type': 'performance',
                'title': 'Field Coverage',
                'description': f"Excellent field utilization at {analysis_data.get('field_coverage', 0.8)*100:.1f}% coverage efficiency",
                'priority': 'medium',
                'impact': 'Spatial Control +8%'
            },
            {
                'type': 'coordination',
                'title': 'Team Coordination',
                'description': f"Strong coordination patterns with {analysis_data.get('coordination_score', 0.85)*100:.1f}% synchronization",
                'priority': 'medium',
                'impact': 'Team Synergy +15%'
            }
        ]

        return insights

    def _get_default_analysis(self) -> Dict:
        """Return default analysis if no data available"""
        return {
            'formation': '4-3-3',
            'formation_stability': 0.85,
            'average_players': 10.5,
            'peak_intensity': 0.92,
            'tactical_switches': 5,
            'field_coverage': 0.78,
            'coordination_score': 0.88,
            'pressure_phases': 12,
            'transition_efficiency': 0.87
        }

# Initialize mock systems
@st.cache_resource
def get_mock_systems():
    """Initialize and cache mock AI systems"""
    return {
        'video_processor': MockVideoProcessor(),
        'ai_analyzer': MockAIAnalyzer()
    }

# Error handling decorator
def safe_execute(func):
    """Decorator for safe function execution with error handling"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            st.error(f"An error occurred: {str(e)}")
            return None
    return wrapper

# Advanced CSS styling for modern dashboard
def load_css():
    st.markdown("""
    <style>
        /* Import Google Fonts */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        /* Global Styles */
        .main .block-container {
            padding-top: 2rem;
            padding-bottom: 2rem;
            max-width: 100%;
        }

        /* Header Styles */
        .main-header {
            font-family: 'Inter', sans-serif;
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        /* Card Styles */
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 1.5rem;
            border-radius: 15px;
            color: white;
            text-align: center;
            margin: 0.5rem 0;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);
        }

        .metric-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .metric-label {
            font-size: 1rem;
            opacity: 0.9;
            font-weight: 500;
        }

        /* Feature Box Styles */
        .feature-box {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border: 2px solid rgba(102, 126, 234, 0.3);
            border-radius: 15px;
            padding: 2rem;
            margin: 1rem 0;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        /* Upload Zone Styles */
        .upload-zone {
            border: 3px dashed #667eea;
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-zone:hover {
            border-color: #764ba2;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            transform: translateY(-2px);
        }

        /* Tab Styles */
        .stTabs [data-baseweb="tab-list"] {
            gap: 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 0.5rem;
        }

        .stTabs [data-baseweb="tab"] {
            height: 50px;
            border-radius: 10px;
            background: transparent;
            border: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .stTabs [aria-selected="true"] {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        /* Button Styles */
        .stButton > button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .stButton > button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        /* Progress Bar Styles */
        .stProgress > div > div > div > div {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        /* Sidebar Styles */
        .css-1d391kg {
            background: linear-gradient(180deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        }

        /* Animation Classes */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        }
    </style>
    """, unsafe_allow_html=True)

load_css()

# Initialize session state
def init_session_state():
    """Initialize session state variables"""
    if 'analysis_history' not in st.session_state:
        st.session_state.analysis_history = []
    if 'current_session_id' not in st.session_state:
        st.session_state.current_session_id = None
    if 'total_players' not in st.session_state:
        st.session_state.total_players = 0
    if 'active_players' not in st.session_state:
        st.session_state.active_players = 0
    if 'tracking_data' not in st.session_state:
        st.session_state.tracking_data = []
    if 'analysis_running' not in st.session_state:
        st.session_state.analysis_running = False
    if 'video_metadata' not in st.session_state:
        st.session_state.video_metadata = {}
    if 'settings' not in st.session_state:
        st.session_state.settings = {
            'detection_threshold': 0.5,
            'tracking_algorithm': 'SORT',
            'frame_skip': 1,
            'export_format': 'JSON'
        }
    if 'active_tab' not in st.session_state:
        st.session_state.active_tab = "upload"

def create_sidebar():
    """Create modern sidebar with navigation and controls"""
    with st.sidebar:
        # Logo and branding
        st.markdown("""
        <div style="text-align: center; padding: 1rem 0;">
            <h2 style="color: #667eea; font-weight: 700; margin: 0;">⚽ Field ID</h2>
            <p style="color: #666; margin: 0; font-size: 0.9rem;">Advanced Sports Analytics</p>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("---")

        # Quick Stats
        st.markdown("### 📊 Quick Stats")
        col1, col2 = st.columns(2)
        with col1:
            sessions_count = len(st.session_state.get('analysis_history', []))
            st.metric("Sessions", sessions_count, delta=None)
        with col2:
            total_players = st.session_state.get('total_players', 0)
            st.metric("Players", total_players, delta=None)

        st.markdown("---")

        # Settings Panel
        with st.expander("⚙️ Analysis Settings", expanded=False):
            st.session_state.settings['detection_threshold'] = st.slider(
                "Detection Threshold", 0.1, 1.0,
                st.session_state.settings['detection_threshold'], 0.1
            )
            st.session_state.settings['tracking_algorithm'] = st.selectbox(
                "Tracking Algorithm",
                ['SORT', 'DeepSORT', 'ByteTrack'],
                index=['SORT', 'DeepSORT', 'ByteTrack'].index(st.session_state.settings['tracking_algorithm'])
            )
            st.session_state.settings['frame_skip'] = st.number_input(
                "Frame Skip", 1, 10, st.session_state.settings['frame_skip']
            )

        st.markdown("---")

        # Workflow Guide
        with st.expander("🗺️ Workflow Guide", expanded=True):
            st.markdown("""
            **📋 Step-by-Step Process:**

            1️⃣ **Upload Video** → Use drag & drop in 'Video Upload' tab

            2️⃣ **Start Analysis** → Switch to 'Live Analysis' tab

            3️⃣ **View Results** → Check 'Analytics Dashboard' tab

            4️⃣ **AI Insights** → Get recommendations in 'AI Insights' tab

            5️⃣ **Export Data** → Download results in 'Data Management' tab
            """)

        # Help & Info
        with st.expander("ℹ️ Help & Tips", expanded=False):
            st.markdown("""
            **🎥 Video Upload:**
            - Supported: MP4, AVI, MOV, MKV
            - Max size: 200MB
            - Best quality: 720p-1080p

            **⚡ Performance Tips:**
            - Use shorter clips for faster processing
            - Ensure good lighting in videos
            - Stable camera position works best

            **📊 Analytics:**
            - Real-time player tracking
            - Movement heatmaps
            - Performance metrics
            """)

def main():
    """Main application function with modern dashboard layout"""
    init_session_state()

    # Header
    st.markdown('<h1 class="main-header fade-in">⚽ Field ID - Advanced Sports Analytics Platform</h1>', unsafe_allow_html=True)

    # Create sidebar
    create_sidebar()

    # Show navigation hints based on user actions
    if st.session_state.get('active_tab') == "analysis":
        st.info("💡 **Tip:** Switch to the '🎯 Live Analysis' tab to start video analysis!")
    elif st.session_state.get('active_tab') == "analytics":
        st.info("💡 **Tip:** Switch to the '📊 Analytics Dashboard' tab to view detailed analytics!")

    # Main dashboard with tabs
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "📹 Video Upload",
        "🎯 Live Analysis",
        "📊 Analytics Dashboard",
        "🤖 AI Insights",
        "💾 Data Management"
    ])
        
    # Tab 1: Video Upload
    with tab1:
        st.markdown("### 📹 Advanced Video Upload")

        # Create modern upload zone
        uploaded_file = create_upload_zone()

        if uploaded_file is not None:
            # Process uploaded video
            process_uploaded_video(uploaded_file)
        else:
            # Show upload instructions
            show_upload_instructions()

    # Tab 2: Live Analysis
    with tab2:
        st.markdown("### 🎯 Live Video Analysis")

        if 'current_video' in st.session_state and st.session_state.current_video is not None:
            # Show current video info
            if 'video_metadata' in st.session_state:
                metadata = st.session_state.video_metadata
                st.markdown(f"""
                <div class="feature-box">
                    <h4 style="color: #667eea;">📹 Current Video: {metadata.get('filename', 'Unknown')}</h4>
                    <p><strong>Duration:</strong> {metadata.get('duration', 0):.1f}s |
                       <strong>Resolution:</strong> {metadata.get('resolution', 'Unknown')} |
                       <strong>FPS:</strong> {metadata.get('fps', 0):.1f}</p>
                </div>
                """, unsafe_allow_html=True)

            create_analysis_interface()
        else:
            st.markdown("""
            <div class="feature-box">
                <h4 style="color: #667eea;">📹 No Video Loaded</h4>
                <p>Please upload a video in the 'Video Upload' tab to start analysis.</p>
                <p><strong>Supported formats:</strong> MP4, AVI, MOV, MKV</p>
                <p><strong>Max file size:</strong> 200MB</p>
            </div>
            """, unsafe_allow_html=True)

    # Tab 3: Analytics Dashboard
    with tab3:
        st.markdown("### 📊 Advanced Analytics Dashboard")
        create_analytics_dashboard()

    # Tab 4: AI Insights
    with tab4:
        st.markdown("### 🤖 AI-Powered Insights")
        create_ai_insights_panel()

    # Tab 5: Data Management
    with tab5:
        st.markdown("### 💾 Data Management & Export")
        create_data_management_panel()

@safe_execute
def create_upload_zone():
    """Create modern drag-and-drop upload zone with validation"""
    st.markdown("""
    <div class="upload-zone fade-in">
        <div style="font-size: 3rem; margin-bottom: 1rem;">📹</div>
        <h3 style="color: #667eea; margin-bottom: 1rem;">Drag & Drop Your Video</h3>
        <p style="color: #666; margin-bottom: 1.5rem;">or click to browse files</p>
        <div style="display: flex; justify-content: center; gap: 1rem; margin-bottom: 1rem;">
            <span style="background: #667eea; color: white; padding: 0.25rem 0.75rem; border-radius: 15px; font-size: 0.8rem;">MP4</span>
            <span style="background: #764ba2; color: white; padding: 0.25rem 0.75rem; border-radius: 15px; font-size: 0.8rem;">AVI</span>
            <span style="background: #f093fb; color: white; padding: 0.25rem 0.75rem; border-radius: 15px; font-size: 0.8rem;">MOV</span>
            <span style="background: #667eea; color: white; padding: 0.25rem 0.75rem; border-radius: 15px; font-size: 0.8rem;">MKV</span>
        </div>
        <p style="color: #999; font-size: 0.9rem;">Maximum file size: 200MB</p>
    </div>
    """, unsafe_allow_html=True)

    # File uploader with validation
    uploaded_file = st.file_uploader(
        "Choose a sports video file",
        type=['mp4', 'avi', 'mov', 'mkv', 'webm'],
        help="Upload a sports video to analyze player movements",
        label_visibility="collapsed"
    )

    # Validate uploaded file
    if uploaded_file is not None:
        if not validate_uploaded_file(uploaded_file):
            return None

    return uploaded_file

def validate_uploaded_file(uploaded_file) -> bool:
    """Validate uploaded file for size and type"""
    try:
        # Check file size (200MB limit)
        max_size = 200 * 1024 * 1024  # 200MB in bytes
        if uploaded_file.size > max_size:
            st.error(f"❌ File too large: {uploaded_file.size / (1024*1024):.1f}MB. Maximum allowed: 200MB")
            return False

        # Check file extension
        allowed_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.webm']
        file_extension = os.path.splitext(uploaded_file.name)[1].lower()
        if file_extension not in allowed_extensions:
            st.error(f"❌ Unsupported file type: {file_extension}. Supported: {', '.join(allowed_extensions)}")
            return False

        # Check minimum file size (1MB)
        min_size = 1 * 1024 * 1024  # 1MB
        if uploaded_file.size < min_size:
            st.warning(f"⚠️ File very small: {uploaded_file.size / (1024*1024):.1f}MB. Results may be limited.")

        return True

    except Exception as e:
        st.error(f"❌ Error validating file: {str(e)}")
        return False

@safe_execute
def process_uploaded_video(uploaded_file):
    """Process and display uploaded video with metadata using mock system"""
    try:
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.mp4') as tmp_file:
            tmp_file.write(uploaded_file.read())
            video_path = tmp_file.name

        # Get mock systems
        mock_systems = get_mock_systems()
        video_processor = mock_systems['video_processor']

        # Store video info in session state
        st.session_state.current_video = video_path
        st.session_state.video_metadata = {
            'filename': uploaded_file.name,
            'size_mb': uploaded_file.size / (1024*1024),
            'upload_time': datetime.now(),
            'session_id': str(uuid.uuid4())
        }

        # Display video information card
        col1, col2 = st.columns([2, 1])

        with col1:
            st.markdown("""
            <div class="feature-box fade-in">
                <h4 style="color: #667eea; margin-bottom: 1rem;">✅ Video Successfully Uploaded</h4>
            </div>
            """, unsafe_allow_html=True)

            # Video player with custom controls
            st.video(uploaded_file)

            # Extract video metadata using mock system
            video_info = video_processor.get_video_info(video_path)

            # Update metadata with mock data
            st.session_state.video_metadata.update(video_info)

    except Exception as e:
        st.error(f"Error processing video: {str(e)}")
        # Provide fallback metadata
        st.session_state.video_metadata = {
            'filename': uploaded_file.name,
            'size_mb': uploaded_file.size / (1024*1024),
            'upload_time': datetime.now(),
            'session_id': str(uuid.uuid4()),
            'fps': 30.0,
            'frame_count': 3000,
            'duration': 100.0,
            'resolution': "1280x720"
        }

    with col2:
        # Video metadata display
        st.markdown("#### 📋 Video Information")

        # Get metadata safely with defaults
        metadata = st.session_state.video_metadata
        duration = metadata.get('duration', 0)
        fps = metadata.get('fps', 0)
        width = metadata.get('width', 0)
        height = metadata.get('height', 0)
        frame_count = metadata.get('frame_count', 0)

        metadata_html = f"""
        <div class="metric-card fade-in">
            <div style="text-align: left;">
                <p><strong>📁 Filename:</strong> {uploaded_file.name}</p>
                <p><strong>📏 Size:</strong> {uploaded_file.size / (1024*1024):.1f} MB</p>
                <p><strong>⏱️ Duration:</strong> {duration:.1f}s</p>
                <p><strong>🎬 FPS:</strong> {fps:.1f}</p>
                <p><strong>📐 Resolution:</strong> {width}x{height}</p>
                <p><strong>🎞️ Frames:</strong> {frame_count:,}</p>
            </div>
        </div>
        """
        st.markdown(metadata_html, unsafe_allow_html=True)

        # Quick action buttons
        st.markdown("#### ⚡ Quick Actions")
        col_a, col_b = st.columns(2)

        with col_a:
            if st.button("🚀 Start Analysis", type="primary", use_container_width=True):
                st.session_state.current_session_id = st.session_state.video_metadata['session_id']
                st.session_state.active_tab = "analysis"
                st.success("🚀 Ready for analysis! Switch to the 'Live Analysis' tab to begin.")

        with col_b:
            if st.button("📊 View Analytics", use_container_width=True):
                st.session_state.active_tab = "analytics"
                st.success("📊 Switch to the 'Analytics Dashboard' tab to view detailed analytics.")

        # Add navigation helper
        st.markdown("""
        <div style="text-align: center; margin-top: 1rem; padding: 1rem; background: rgba(102, 126, 234, 0.1); border-radius: 10px;">
            <p style="margin: 0; color: #667eea; font-weight: 500;">
                💡 <strong>Next Steps:</strong> Use the tabs above to navigate between different features
            </p>
        </div>
        """, unsafe_allow_html=True)

def show_upload_instructions():
    """Show upload instructions and tips"""
    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown("""
        <div class="feature-box fade-in">
            <h4 style="color: #667eea;">🎥 Supported Formats</h4>
            <ul style="text-align: left;">
                <li>MP4 (Recommended)</li>
                <li>AVI</li>
                <li>MOV</li>
                <li>MKV</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown("""
        <div class="feature-box fade-in">
            <h4 style="color: #764ba2;">⚡ Best Practices</h4>
            <ul style="text-align: left;">
                <li>720p-1080p resolution</li>
                <li>Stable camera position</li>
                <li>Good lighting conditions</li>
                <li>Clear player visibility</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        st.markdown("""
        <div class="feature-box fade-in">
            <h4 style="color: #f093fb;">📏 File Limits</h4>
            <ul style="text-align: left;">
                <li>Maximum: 200MB</li>
                <li>Duration: Up to 10 minutes</li>
                <li>Minimum: 480p resolution</li>
                <li>Frame rate: 15-60 FPS</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)
    
@safe_execute
def create_analysis_interface():
    """Create the live analysis interface with interactive controls"""

    # Analysis control panel
    col1, col2, col3, col4 = st.columns([2, 1, 1, 1])

    with col1:
        # Video player with custom controls
        if 'current_video' in st.session_state:
            try:
                st.video(st.session_state.current_video)
            except Exception as e:
                st.warning("Video preview not available. Analysis can still proceed.")

    with col2:
        if st.button("🚀 Start Analysis", type="primary", use_container_width=True):
            start_analysis()

    with col3:
        if st.button("⏸️ Pause", use_container_width=True):
            st.session_state.analysis_running = False
            st.info("⏸️ Analysis paused")

    with col4:
        if st.button("⏹️ Stop", use_container_width=True):
            stop_analysis()

    # Progress and status for running analysis
    if st.session_state.get('analysis_running', False):
        simulate_real_time_analysis()

    # Real-time statistics
    col1, col2 = st.columns([1, 1])

    with col1:
        st.markdown("#### 📊 Real-time Statistics")
        create_live_metrics()

    with col2:
        st.markdown("#### 🎯 Detection Overlay")
        create_detection_overlay()

@safe_execute
def simulate_real_time_analysis():
    """Simulate real-time video analysis with mock data"""
    progress_placeholder = st.empty()
    status_placeholder = st.empty()
    metrics_placeholder = st.empty()

    # Get mock systems
    mock_systems = get_mock_systems()
    video_processor = mock_systems['video_processor']

    # Simulate analysis progress
    total_frames = st.session_state.video_metadata.get('frame_count', 3000)

    for i in range(0, 101, 2):  # Progress in 2% increments for faster demo
        if not st.session_state.get('analysis_running', False):
            break

        current_frame = int((i / 100) * total_frames)
        progress_placeholder.progress(i)
        status_placeholder.text(f"🎯 Processing frame {current_frame:,}/{total_frames:,} ({i}%)")

        # Generate mock detection data
        detection_data = video_processor.simulate_player_detection(current_frame)

        # Update session state with new data
        if 'tracking_data' not in st.session_state:
            st.session_state.tracking_data = []

        st.session_state.tracking_data.append(detection_data)
        st.session_state.total_players = detection_data['total_players']
        st.session_state.active_players = detection_data['visible_players']

        # Update metrics display
        with metrics_placeholder.container():
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Players Detected", detection_data['total_players'])
            with col2:
                st.metric("Currently Visible", detection_data['visible_players'])
            with col3:
                st.metric("Confidence", f"{detection_data['detection_confidence']*100:.1f}%")

        time.sleep(0.1)  # Small delay for visual effect

    if st.session_state.get('analysis_running', False):
        status_placeholder.success("✅ Analysis completed successfully!")
        st.session_state.analysis_running = False

def create_live_metrics():
    """Create live metrics display with modern cards"""

    # Generate sample data for demonstration
    if st.session_state.get('analysis_running', False):
        st.session_state.total_players = np.random.randint(8, 15)
        st.session_state.active_players = np.random.randint(5, st.session_state.get('total_players', 10))

    # Get values safely with defaults
    total_players = st.session_state.get('total_players', 0)
    active_players = st.session_state.get('active_players', 0)
    tracking_data = st.session_state.get('tracking_data', [])

    # Metrics cards
    metrics_html = f"""
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin: 1rem 0;">
        <div class="metric-card pulse">
            <div class="metric-number">{total_players}</div>
            <div class="metric-label">Total Players</div>
        </div>
        <div class="metric-card pulse">
            <div class="metric-number">{active_players}</div>
            <div class="metric-label">Active Players</div>
        </div>
        <div class="metric-card pulse">
            <div class="metric-number">{max(0, total_players - active_players)}</div>
            <div class="metric-label">Inactive Players</div>
        </div>
        <div class="metric-card pulse">
            <div class="metric-number">{len(tracking_data)}</div>
            <div class="metric-label">Tracking Points</div>
        </div>
    </div>
    """
    st.markdown(metrics_html, unsafe_allow_html=True)

def create_detection_overlay():
    """Create detection overlay visualization"""

    # Sample detection data
    if st.session_state.tracking_data:
        # Create a simple visualization of detections
        fig = go.Figure()

        # Add sample player positions
        x_positions = np.random.randint(0, 100, st.session_state.active_players)
        y_positions = np.random.randint(0, 60, st.session_state.active_players)

        fig.add_trace(go.Scatter(
            x=x_positions,
            y=y_positions,
            mode='markers+text',
            marker=dict(
                size=15,
                color='rgba(102, 126, 234, 0.8)',
                line=dict(width=2, color='white')
            ),
            text=[f'P{i+1}' for i in range(st.session_state.active_players)],
            textposition="middle center",
            textfont=dict(color="white", size=10),
            name="Players"
        ))

        fig.update_layout(
            title="Player Positions",
            xaxis_title="Field Width",
            yaxis_title="Field Height",
            showlegend=False,
            height=300,
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)'
        )

        st.plotly_chart(fig, use_container_width=True)
    else:
        st.info("🎯 Start analysis to see player detection overlay")

def start_analysis():
    """Start video analysis"""
    st.session_state.analysis_running = True
    st.session_state.current_session_id = str(uuid.uuid4())

    # Add to analysis history
    session_data = {
        'id': st.session_state.current_session_id,
        'video_name': st.session_state.video_metadata.get('filename', 'Unknown'),
        'start_time': datetime.now(),
        'status': 'running'
    }
    st.session_state.analysis_history.append(session_data)

    st.success("🚀 Analysis started!")

def stop_analysis():
    """Stop video analysis"""
    st.session_state.analysis_running = False

    # Update history
    if st.session_state.analysis_history:
        st.session_state.analysis_history[-1]['status'] = 'completed'
        st.session_state.analysis_history[-1]['end_time'] = datetime.now()

    st.info("⏹️ Analysis stopped")

def create_analytics_dashboard():
    """Create comprehensive analytics dashboard with interactive charts"""

    tracking_data = st.session_state.get('tracking_data', [])

    if not tracking_data:
        st.info("📊 No tracking data available. Please analyze a video first to see analytics.")

        # Show sample dashboard
        st.markdown("#### 📈 Sample Analytics Dashboard")
        create_sample_dashboard()
        return

    # Dashboard controls
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        chart_type = st.selectbox("📊 Chart Type", ["Line", "Bar", "Area", "Scatter"])

    with col2:
        time_range = st.selectbox("⏰ Time Range", ["All", "Last 5min", "Last 10min", "Custom"])

    with col3:
        metric = st.selectbox("📏 Metric", ["Player Count", "Activity Level", "Movement Speed", "Coverage Area"])

    with col4:
        if st.button("🔄 Refresh Data", use_container_width=True):
            st.rerun()

    # Main analytics grid
    col1, col2 = st.columns([2, 1])

    with col1:
        # Primary chart
        create_primary_chart(chart_type, metric)

        # Heatmap
        st.markdown("#### 🔥 Player Movement Heatmap")
        create_movement_heatmap()

    with col2:
        # KPI cards
        create_kpi_cards()

        # Player activity timeline
        st.markdown("#### ⏱️ Activity Timeline")
        create_activity_timeline()

def create_sample_dashboard():
    """Create sample dashboard for demonstration"""

    # Generate sample data
    dates = pd.date_range(start='2024-01-01', periods=30, freq='D')
    sample_data = pd.DataFrame({
        'date': dates,
        'total_players': np.random.randint(8, 15, 30),
        'active_players': np.random.randint(5, 12, 30),
        'avg_speed': np.random.uniform(2.5, 8.5, 30),
        'coverage_area': np.random.uniform(60, 95, 30)
    })

    # Main metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Avg Players", f"{sample_data['total_players'].mean():.1f}", delta="1.2")

    with col2:
        st.metric("Activity Rate", f"{(sample_data['active_players'].mean() / sample_data['total_players'].mean() * 100):.1f}%", delta="5.3%")

    with col3:
        st.metric("Avg Speed", f"{sample_data['avg_speed'].mean():.1f} m/s", delta="0.8")

    with col4:
        st.metric("Field Coverage", f"{sample_data['coverage_area'].mean():.1f}%", delta="-2.1%")

    # Charts
    col1, col2 = st.columns(2)

    with col1:
        # Player count over time
        fig = px.line(sample_data, x='date', y=['total_players', 'active_players'],
                     title="Player Activity Over Time",
                     color_discrete_map={'total_players': '#667eea', 'active_players': '#764ba2'})
        fig.update_layout(height=400, plot_bgcolor='rgba(0,0,0,0)', paper_bgcolor='rgba(0,0,0,0)')
        st.plotly_chart(fig, use_container_width=True)

    with col2:
        # Speed distribution
        fig = px.histogram(sample_data, x='avg_speed', nbins=15,
                          title="Speed Distribution",
                          color_discrete_sequence=['#f093fb'])
        fig.update_layout(height=400, plot_bgcolor='rgba(0,0,0,0)', paper_bgcolor='rgba(0,0,0,0)')
        st.plotly_chart(fig, use_container_width=True)

def create_primary_chart(chart_type, metric):
    """Create primary analytics chart"""

    # Generate sample time series data
    timestamps = pd.date_range(start=datetime.now() - timedelta(minutes=30),
                              end=datetime.now(), freq='30S')

    if metric == "Player Count":
        values = np.random.randint(5, 15, len(timestamps))
        title = "Player Count Over Time"
        y_label = "Number of Players"
    elif metric == "Activity Level":
        values = np.random.uniform(0.3, 1.0, len(timestamps))
        title = "Player Activity Level"
        y_label = "Activity Score"
    elif metric == "Movement Speed":
        values = np.random.uniform(1.0, 10.0, len(timestamps))
        title = "Average Movement Speed"
        y_label = "Speed (m/s)"
    else:  # Coverage Area
        values = np.random.uniform(60, 95, len(timestamps))
        title = "Field Coverage Area"
        y_label = "Coverage (%)"

    df = pd.DataFrame({'timestamp': timestamps, 'value': values})

    if chart_type == "Line":
        fig = px.line(df, x='timestamp', y='value', title=title)
    elif chart_type == "Bar":
        fig = px.bar(df, x='timestamp', y='value', title=title)
    elif chart_type == "Area":
        fig = px.area(df, x='timestamp', y='value', title=title)
    else:  # Scatter
        fig = px.scatter(df, x='timestamp', y='value', title=title)

    fig.update_layout(
        height=400,
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)',
        yaxis_title=y_label
    )
    fig.update_traces(line_color='#667eea', marker_color='#667eea')

    st.plotly_chart(fig, use_container_width=True)

def create_movement_heatmap():
    """Create player movement heatmap"""

    # Generate sample heatmap data
    x = np.random.randint(0, 100, 200)
    y = np.random.randint(0, 60, 200)

    fig = go.Figure(data=go.Histogram2d(
        x=x, y=y,
        colorscale='Viridis',
        showscale=True
    ))

    fig.update_layout(
        title="Player Movement Density",
        xaxis_title="Field Width (m)",
        yaxis_title="Field Height (m)",
        height=300,
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)'
    )

    st.plotly_chart(fig, use_container_width=True)

def create_kpi_cards():
    """Create KPI cards for key metrics"""

    kpis = [
        {"title": "Detection Accuracy", "value": "94.2%", "delta": "+2.1%", "color": "#667eea"},
        {"title": "Tracking Stability", "value": "87.8%", "delta": "+0.5%", "color": "#764ba2"},
        {"title": "Processing Speed", "value": "28.5 FPS", "delta": "-1.2", "color": "#f093fb"},
        {"title": "Data Quality", "value": "91.7%", "delta": "+3.4%", "color": "#667eea"}
    ]

    for kpi in kpis:
        st.markdown(f"""
        <div class="metric-card" style="background: linear-gradient(135deg, {kpi['color']} 0%, rgba(255,255,255,0.1) 100%); margin-bottom: 1rem;">
            <div style="font-size: 1.5rem; font-weight: 700; margin-bottom: 0.5rem;">{kpi['value']}</div>
            <div style="font-size: 0.9rem; opacity: 0.9; margin-bottom: 0.25rem;">{kpi['title']}</div>
            <div style="font-size: 0.8rem; color: #90EE90;">{kpi['delta']}</div>
        </div>
        """, unsafe_allow_html=True)

def create_activity_timeline():
    """Create activity timeline chart"""

    # Generate sample timeline data
    times = pd.date_range(start=datetime.now() - timedelta(minutes=10),
                         end=datetime.now(), freq='1min')
    activities = np.random.choice(['High', 'Medium', 'Low'], len(times), p=[0.3, 0.5, 0.2])

    color_map = {'High': '#ff6b6b', 'Medium': '#feca57', 'Low': '#48dbfb'}
    colors = [color_map[activity] for activity in activities]

    fig = go.Figure(data=go.Scatter(
        x=times,
        y=activities,
        mode='markers+lines',
        marker=dict(size=10, color=colors),
        line=dict(color='#667eea', width=2)
    ))

    fig.update_layout(
        height=200,
        showlegend=False,
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)',
        margin=dict(l=0, r=0, t=0, b=0)
    )

    st.plotly_chart(fig, use_container_width=True)

def create_ai_insights_panel():
    """Create AI insights and recommendations panel"""

    if not st.session_state.tracking_data:
        st.info("🤖 No analysis data available. Please analyze a video first to see AI insights.")

        # Show sample insights
        create_sample_insights()
        return

    # AI Insights tabs
    insight_tab1, insight_tab2, insight_tab3 = st.tabs([
        "🧠 Smart Analysis",
        "📈 Performance Insights",
        "🎯 Recommendations"
    ])

    with insight_tab1:
        create_smart_analysis()

    with insight_tab2:
        create_performance_insights()

    with insight_tab3:
        create_recommendations()

def create_sample_insights():
    """Create sample AI insights for demonstration"""

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("""
        <div class="feature-box fade-in">
            <h4 style="color: #667eea;">🧠 AI Analysis Summary</h4>
            <p><strong>Game Flow:</strong> High-intensity match with consistent player movement patterns.</p>
            <p><strong>Player Distribution:</strong> Balanced field coverage with strategic positioning.</p>
            <p><strong>Activity Peaks:</strong> Maximum activity detected at 15:30 and 42:15 timestamps.</p>
            <p><strong>Tracking Quality:</strong> Excellent visibility conditions with 94.2% detection accuracy.</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown("""
        <div class="feature-box fade-in">
            <h4 style="color: #764ba2;">📊 Key Metrics</h4>
            <p><strong>Average Players:</strong> 11.3 players per frame</p>
            <p><strong>Movement Speed:</strong> 4.7 m/s average</p>
            <p><strong>Field Coverage:</strong> 78.5% of playing area</p>
            <p><strong>Formation Changes:</strong> 12 tactical shifts detected</p>
        </div>
        """, unsafe_allow_html=True)

    # Generate AI summary
    if st.button("✨ Generate Detailed AI Summary", type="primary", use_container_width=True):
        with st.spinner("🤖 AI is analyzing the game..."):
            time.sleep(3)
            generate_ai_summary()

def create_smart_analysis():
    """Create smart analysis section"""

    st.markdown("#### 🧠 Intelligent Game Analysis")

    # Analysis metrics
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("Formation Stability", "87%", delta="5%")

    with col2:
        st.metric("Tactical Switches", "8", delta="2")

    with col3:
        st.metric("Pressure Intensity", "High", delta="Medium→High")

    # Smart insights
    insights = [
        "🎯 **Tactical Pattern**: Team shows preference for wide formations during attacking phases",
        "⚡ **High Activity Zones**: Central midfield area shows 40% higher player density",
        "🔄 **Rotation Analysis**: Detected 3 systematic player rotations in defensive transitions",
        "📊 **Efficiency Score**: Team maintains 78% positional efficiency throughout the match"
    ]

    for insight in insights:
        st.markdown(f"<div class='feature-box'>{insight}</div>", unsafe_allow_html=True)

def create_performance_insights():
    """Create performance insights section"""

    st.markdown("#### 📈 Performance Analytics")

    # Performance radar chart
    categories = ['Speed', 'Coverage', 'Coordination', 'Intensity', 'Positioning']
    values = [85, 78, 92, 88, 76]

    fig = go.Figure()

    fig.add_trace(go.Scatterpolar(
        r=values,
        theta=categories,
        fill='toself',
        name='Team Performance',
        line_color='#667eea',
        fillcolor='rgba(102, 126, 234, 0.3)'
    ))

    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 100]
            )),
        showlegend=False,
        height=400,
        title="Team Performance Radar"
    )

    st.plotly_chart(fig, use_container_width=True)

    # Performance breakdown
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("##### 🏃 Movement Analysis")
        movement_data = pd.DataFrame({
            'Player': [f'Player {i+1}' for i in range(5)],
            'Distance': np.random.uniform(8.5, 12.3, 5),
            'Speed': np.random.uniform(4.2, 7.8, 5)
        })
        st.dataframe(movement_data, use_container_width=True)

    with col2:
        st.markdown("##### 🎯 Positioning Efficiency")
        efficiency_data = pd.DataFrame({
            'Zone': ['Defense', 'Midfield', 'Attack'],
            'Efficiency': [87, 92, 78],
            'Coverage': [95, 88, 82]
        })
        st.dataframe(efficiency_data, use_container_width=True)

def create_recommendations():
    """Create AI recommendations section"""

    st.markdown("#### 🎯 AI-Powered Recommendations")

    recommendations = [
        {
            "title": "🔄 Tactical Adjustment",
            "description": "Consider increasing midfield density during defensive phases",
            "priority": "High",
            "impact": "Defensive Stability +15%"
        },
        {
            "title": "⚡ Player Rotation",
            "description": "Implement systematic rotation in high-intensity zones",
            "priority": "Medium",
            "impact": "Endurance +12%"
        },
        {
            "title": "📊 Formation Optimization",
            "description": "Adjust wide player positioning for better field coverage",
            "priority": "Medium",
            "impact": "Coverage +8%"
        },
        {
            "title": "🎯 Training Focus",
            "description": "Focus on coordination drills for transition phases",
            "priority": "Low",
            "impact": "Efficiency +10%"
        }
    ]

    for i, rec in enumerate(recommendations):
        priority_color = {"High": "#ff6b6b", "Medium": "#feca57", "Low": "#48dbfb"}[rec["priority"]]

        st.markdown(f"""
        <div class="feature-box fade-in">
            <h5 style="color: #667eea; margin-bottom: 0.5rem;">{rec['title']}</h5>
            <p style="margin-bottom: 0.5rem;">{rec['description']}</p>
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span style="background: {priority_color}; color: white; padding: 0.25rem 0.75rem; border-radius: 15px; font-size: 0.8rem;">
                    {rec['priority']} Priority
                </span>
                <span style="color: #28a745; font-weight: 600; font-size: 0.9rem;">
                    {rec['impact']}
                </span>
            </div>
        </div>
        """, unsafe_allow_html=True)

def create_data_management_panel():
    """Create data management and export panel"""

    st.markdown("#### 💾 Data Management & Export Options")

    # Export options
    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown("##### 📊 Export Formats")
        export_format = st.selectbox(
            "Choose format:",
            ["JSON", "CSV", "Excel", "PDF Report"]
        )

    with col2:
        st.markdown("##### 📅 Date Range")
        date_range = st.selectbox(
            "Select range:",
            ["Current Session", "Last 7 Days", "Last 30 Days", "All Data"]
        )

    with col3:
        st.markdown("##### 🎯 Data Type")
        data_type = st.multiselect(
            "Include:",
            ["Tracking Data", "Statistics", "AI Insights", "Performance Metrics"],
            default=["Tracking Data", "Statistics"]
        )

    # Export buttons
    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("📥 Export Data", type="primary", use_container_width=True):
            export_data(export_format, date_range, data_type)

    with col2:
        if st.button("📋 Generate Report", use_container_width=True):
            generate_report()

    with col3:
        if st.button("🗑️ Clear Data", use_container_width=True):
            clear_data()

    # Session history
    st.markdown("#### 📚 Analysis History")

    if st.session_state.analysis_history:
        history_df = pd.DataFrame(st.session_state.analysis_history)
        st.dataframe(history_df, use_container_width=True)
    else:
        st.info("📝 No analysis history available yet.")

    # Data storage info
    st.markdown("#### 💽 Storage Information")

    storage_info = f"""
    <div class="feature-box">
        <p><strong>📊 Tracking Points:</strong> {len(st.session_state.tracking_data):,}</p>
        <p><strong>📁 Sessions:</strong> {len(st.session_state.analysis_history)}</p>
        <p><strong>💾 Estimated Size:</strong> {len(str(st.session_state.tracking_data)) / 1024:.1f} KB</p>
        <p><strong>🕒 Last Updated:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    """
    st.markdown(storage_info, unsafe_allow_html=True)

def export_data(format_type, date_range, data_types):
    """Export data in specified format"""

    # Prepare export data
    export_data = {
        'metadata': {
            'export_time': datetime.now().isoformat(),
            'format': format_type,
            'date_range': date_range,
            'data_types': data_types
        }
    }

    if 'Tracking Data' in data_types:
        export_data['tracking_data'] = st.session_state.tracking_data

    if 'Statistics' in data_types:
        export_data['statistics'] = {
            'total_players': st.session_state.total_players,
            'active_players': st.session_state.active_players,
            'tracking_points': len(st.session_state.tracking_data)
        }

    if 'AI Insights' in data_types:
        export_data['ai_insights'] = st.session_state.get('ai_summary', 'No insights generated')

    # Convert to appropriate format
    if format_type == "JSON":
        json_data = json.dumps(export_data, indent=2)
        st.download_button(
            label="📥 Download JSON",
            data=json_data,
            file_name=f"field_id_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            mime="application/json"
        )
    elif format_type == "CSV":
        if st.session_state.tracking_data:
            df = pd.DataFrame(st.session_state.tracking_data)
            csv_data = df.to_csv(index=False)
            st.download_button(
                label="📥 Download CSV",
                data=csv_data,
                file_name=f"field_id_tracking_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )

    st.success(f"✅ {format_type} export prepared successfully!")

def generate_report():
    """Generate comprehensive analysis report"""

    with st.spinner("📋 Generating comprehensive report..."):
        time.sleep(2)

        report_html = f"""
        <div class="feature-box">
            <h3 style="color: #667eea;">📋 Field ID Analysis Report</h3>
            <p><strong>Generated:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p><strong>Session ID:</strong> {st.session_state.get('current_session_id', 'N/A')}</p>
            <hr>
            <h4>📊 Summary Statistics</h4>
            <ul>
                <li>Total Players Detected: {st.session_state.total_players}</li>
                <li>Active Players: {st.session_state.active_players}</li>
                <li>Tracking Data Points: {len(st.session_state.tracking_data)}</li>
                <li>Analysis Duration: {len(st.session_state.tracking_data) * 0.5:.1f} seconds</li>
            </ul>
            <h4>🎯 Key Insights</h4>
            <p>{st.session_state.get('ai_summary', 'No AI summary available')}</p>
        </div>
        """

        st.markdown(report_html, unsafe_allow_html=True)
        st.success("📋 Report generated successfully!")

def clear_data():
    """Clear all analysis data"""

    if st.button("⚠️ Confirm Clear All Data", type="secondary"):
        st.session_state.tracking_data = []
        st.session_state.analysis_history = []
        st.session_state.total_players = 0
        st.session_state.active_players = 0
        st.session_state.current_session_id = None

        if 'ai_summary' in st.session_state:
            del st.session_state.ai_summary

        st.success("🗑️ All data cleared successfully!")
        st.rerun()

@safe_execute
def generate_ai_summary():
    """Generate comprehensive AI summary with advanced insights using mock analyzer"""

    with st.spinner("🤖 Advanced AI is analyzing the game..."):
        time.sleep(2)  # Simulate AI processing

        # Get mock systems
        mock_systems = get_mock_systems()
        ai_analyzer = mock_systems['ai_analyzer']

        # Get tracking data safely
        tracking_data = st.session_state.get('tracking_data', [])
        total_players = st.session_state.get('total_players', 0)

        # Generate analysis using mock AI
        analysis_data = ai_analyzer.analyze_game_flow(tracking_data)
        insights = ai_analyzer.generate_insights(analysis_data)

        # Calculate statistics
        avg_active = np.mean([d.get('visible_players', 0) for d in tracking_data]) if tracking_data else 0

        summary = f"""
        ## 🎯 Advanced AI Game Analysis

        ### 📊 **Statistical Overview**
        - **Total Players Identified:** {total_players} unique players
        - **Average Active Players:** {avg_active:.1f} players per frame
        - **Tracking Data Points:** {len(tracking_data):,} measurements
        - **Analysis Confidence:** {analysis_data.get('peak_intensity', 0.94)*100:.1f}% accuracy rate

        ### 🏃 **Movement & Tactical Analysis**
        - **Formation:** {analysis_data.get('formation', '4-3-3')} with {analysis_data.get('formation_stability', 0.85)*100:.1f}% stability
        - **Field Coverage:** {analysis_data.get('field_coverage', 0.78)*100:.1f}% effective area utilization
        - **Tactical Switches:** {analysis_data.get('tactical_switches', 5)} formation changes detected
        - **Coordination Score:** {analysis_data.get('coordination_score', 0.88)*100:.1f}% team synchronization

        ### ⚡ **Performance Insights**
        - **Pressure Phases:** {analysis_data.get('pressure_phases', 12)} high-intensity periods identified
        - **Transition Efficiency:** {analysis_data.get('transition_efficiency', 0.87)*100:.1f}% successful transitions
        - **Average Player Count:** {analysis_data.get('average_players', 10.5):.1f} players per frame
        - **Peak Intensity:** {analysis_data.get('peak_intensity', 0.92)*100:.1f}% maximum activity level

        ### 🎮 **Technical Quality Assessment**
        - **Video Quality:** Excellent conditions for optimal player tracking
        - **Detection Accuracy:** {analysis_data.get('peak_intensity', 0.94)*100:.1f}% player identification rate
        - **Tracking Continuity:** High-quality continuous player monitoring
        - **Data Reliability:** Professional-grade measurement accuracy

        ### 🔮 **AI-Generated Insights**
        """

        # Add insights from mock AI
        for insight in insights:
            summary += f"""
        - **{insight['title']}:** {insight['description']} (Impact: {insight['impact']})"""

        st.session_state.ai_summary = summary
        st.session_state.ai_analysis_data = analysis_data
        st.session_state.ai_insights = insights

        # Display the summary in a beautiful format
        st.markdown("""
        <div class="feature-box fade-in">
            <h3 style="color: #667eea; text-align: center; margin-bottom: 2rem;">🤖 AI Analysis Complete</h3>
        </div>
        """, unsafe_allow_html=True)

        st.markdown(summary)

        # Add action buttons
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("📊 View Detailed Analytics", use_container_width=True):
                st.session_state.active_tab = "analytics"
                st.success("📊 Switch to the 'Analytics Dashboard' tab to view detailed analytics.")

        with col2:
            st.download_button(
                label="📥 Export Summary",
                data=summary,
                file_name=f"ai_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
                mime="text/markdown",
                use_container_width=True
            )

        with col3:
            if st.button("🔄 Regenerate Analysis", use_container_width=True):
                # Clear previous analysis and regenerate
                if 'ai_summary' in st.session_state:
                    del st.session_state.ai_summary
                generate_ai_summary()

# Add keyboard shortcuts and advanced features
def add_keyboard_shortcuts():
    """Add keyboard shortcuts for common actions"""

    st.markdown("""
    <script>
    document.addEventListener('keydown', function(e) {
        // Ctrl+U for upload
        if (e.ctrlKey && e.key === 'u') {
            e.preventDefault();
            // Trigger upload action
        }

        // Space for play/pause
        if (e.key === ' ') {
            e.preventDefault();
            // Trigger play/pause
        }

        // Ctrl+E for export
        if (e.ctrlKey && e.key === 'e') {
            e.preventDefault();
            // Trigger export
        }

        // Ctrl+R for refresh
        if (e.ctrlKey && e.key === 'r') {
            e.preventDefault();
            // Trigger refresh
        }
    });
    </script>
    """, unsafe_allow_html=True)

def add_help_tooltips():
    """Add help tooltips throughout the interface"""

    st.markdown("""
    <style>
    .tooltip {
        position: relative;
        display: inline-block;
        cursor: help;
    }

    .tooltip .tooltiptext {
        visibility: hidden;
        width: 200px;
        background-color: #667eea;
        color: white;
        text-align: center;
        border-radius: 6px;
        padding: 8px;
        position: absolute;
        z-index: 1;
        bottom: 125%;
        left: 50%;
        margin-left: -100px;
        opacity: 0;
        transition: opacity 0.3s;
        font-size: 12px;
    }

    .tooltip:hover .tooltiptext {
        visibility: visible;
        opacity: 1;
    }
    </style>
    """, unsafe_allow_html=True)

# Add footer with additional information
def add_footer():
    """Add professional footer with information and links"""

    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; padding: 2rem 0; color: #666;">
        <h4 style="color: #667eea; margin-bottom: 1rem;">⚽ Field ID - Advanced Sports Analytics</h4>
        <p style="margin-bottom: 0.5rem;">Powered by AI • Real-time Player Tracking • Professional Analytics</p>
        <p style="font-size: 0.9rem; margin-bottom: 1rem;">
            🚀 <strong>Version 2.0</strong> |
            📊 <strong>94.2% Accuracy</strong> |
            ⚡ <strong>Real-time Processing</strong> |
            🤖 <strong>AI-Powered Insights</strong>
        </p>
        <div style="display: flex; justify-content: center; gap: 2rem; margin-top: 1rem;">
            <span style="color: #667eea;">📧 <EMAIL></span>
            <span style="color: #764ba2;">🌐 www.fieldid.ai</span>
            <span style="color: #f093fb;">📱 Mobile App Available</span>
        </div>
    </div>
    """, unsafe_allow_html=True)

# Performance monitoring
def add_performance_monitoring():
    """Add performance monitoring and optimization"""

    # Ensure session state is initialized first
    if 'tracking_data' not in st.session_state:
        return

    # Monitor session state size
    session_size = len(str(st.session_state))

    if session_size > 1000000:  # 1MB
        st.warning("⚠️ Large session data detected. Consider clearing old data for better performance.")

    # Add performance metrics in sidebar
    with st.sidebar:
        with st.expander("⚡ Performance Metrics", expanded=False):
            st.metric("Session Size", f"{session_size / 1024:.1f} KB")
            st.metric("Tracking Points", len(st.session_state.tracking_data))
            st.metric("Memory Usage", "Optimized")

if __name__ == "__main__":
    # Add advanced features (before main to avoid session state issues)
    add_keyboard_shortcuts()
    add_help_tooltips()

    # Run main application (this initializes session state)
    main()

    # Add performance monitoring after session state is initialized
    add_performance_monitoring()

    # Add footer
    add_footer()

# Copy essential project files to create a fresh repository
$source = "studio-master"
$dest = "field-id-fresh"

# Create destination directory
New-Item -ItemType Directory -Force -Path $dest

# Copy root files
Copy-Item "$source\*.json" $dest -Force
Copy-Item "$source\*.md" $dest -Force  
Copy-Item "$source\*.py" $dest -Force
Copy-Item "$source\*.txt" $dest -Force
Copy-Item "$source\*.ts" $dest -Force
Copy-Item "$source\*.mjs" $dest -Force
Copy-Item "$source\*.yaml" $dest -Force
Copy-Item "$source\*.toml" $dest -Force
Copy-Item "$source\.env.example" $dest -Force
Copy-Item "$source\.gitignore" $dest -Force

# Copy directories
Copy-Item "$source\src" $dest -Recurse -Force
Copy-Item "$source\public" $dest -Recurse -Force
Copy-Item "$source\scripts" $dest -Recurse -Force
Copy-Item "$source\docs" $dest -Recurse -Force
Copy-Item "$source\.github" $dest -Recurse -Force
Copy-Item "$source\.streamlit" $dest -Recurse -Force
Copy-Item "$source\.idx" $dest -Recurse -Force

Write-Host "Project files copied successfully to $dest"

{"name": "field-id", "version": 2, "framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm ci", "devCommand": "npm run dev", "env": {"GOOGLE_GENAI_API_KEY": "@google-genai-api-key"}, "build": {"env": {"GOOGLE_GENAI_API_KEY": "@google-genai-api-key"}}, "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/model.onnx", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/onnx-wasm/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "Cross-Origin-Embedder-Policy", "value": "require-corp"}, {"key": "Cross-Origin-Opener-Policy", "value": "same-origin"}]}], "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}]}
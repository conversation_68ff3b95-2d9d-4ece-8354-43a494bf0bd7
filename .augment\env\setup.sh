#!/bin/bash
set -e

# Try to fetch from remote and checkout content
echo "=== Fetching from remote ==="
git fetch origin

echo "=== Check remote branches after fetch ==="
git branch -r

echo "=== Try to checkout main branch from origin ==="
if git show-ref --verify --quiet refs/heads/main; then
	git checkout main
else
	git checkout -b main origin/main
fi

echo "=== Check if there are other branches ==="
git branch -a

echo "=== Try to pull latest changes ==="
git pull origin main

echo "=== Final directory contents ==="
ls -la

echo "=== Find all files after potential checkout ==="
find . -type f -not -path "./.git/*" | head -20
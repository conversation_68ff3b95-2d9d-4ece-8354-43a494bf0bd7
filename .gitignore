# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

.genkit/*

# Environment variables (keep .env.example for reference)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# API Keys and secrets (extra security)
**/api-keys.txt
**/secrets.txt
**/*secret*
**/*key*.txt

# firebase
firebase-debug.log
firestore-debug.log

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Temporary files
tmp/
temp/

# Model files (large files - using external hosting)
public/model.onnx
best.pt
*.onnx
*.pt

# WASM files (large files - can be downloaded from CDN)
public/onnx-wasm/
*.wasm

# Build artifacts
*.map
dist/

# Testing
.nyc_output/

# Deployment
.vercel
.netlify
#!/usr/bin/env node

/**
 * Download script for the ONNX model file
 * This script downloads the model from external hosting
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

// Model download URL (you'll need to host this somewhere)
const MODEL_URL = process.env.MODEL_URL || 'https://github.com/your-username/field-id-models/releases/download/v1.0.0/model.onnx';
const MODEL_PATH = path.join(__dirname, '..', 'public', 'model.onnx');

function downloadModel() {
  console.log('📥 Downloading ONNX model...');
  console.log(`URL: ${MODEL_URL}`);
  console.log(`Destination: ${MODEL_PATH}`);
  
  // Check if model already exists
  if (fs.existsSync(MODEL_PATH)) {
    console.log('✅ Model file already exists. Skipping download.');
    console.log('💡 To force re-download, delete public/model.onnx and run this script again.');
    return;
  }
  
  // Create public directory if it doesn't exist
  const publicDir = path.dirname(MODEL_PATH);
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
  }
  
  const file = fs.createWriteStream(MODEL_PATH);
  
  https.get(MODEL_URL, (response) => {
    if (response.statusCode !== 200) {
      console.error(`❌ Failed to download model: HTTP ${response.statusCode}`);
      console.error('💡 Please check the MODEL_URL or download the model manually.');
      console.error('📖 See README.md for manual download instructions.');
      process.exit(1);
    }
    
    const totalSize = parseInt(response.headers['content-length'], 10);
    let downloadedSize = 0;
    
    response.on('data', (chunk) => {
      downloadedSize += chunk.length;
      const progress = ((downloadedSize / totalSize) * 100).toFixed(1);
      process.stdout.write(`\r📊 Progress: ${progress}% (${(downloadedSize / 1024 / 1024).toFixed(1)}MB / ${(totalSize / 1024 / 1024).toFixed(1)}MB)`);
    });
    
    response.pipe(file);
    
    file.on('finish', () => {
      file.close();
      console.log('\n✅ Model downloaded successfully!');
      console.log('🚀 You can now run the application with: npm run dev');
    });
    
    file.on('error', (err) => {
      fs.unlink(MODEL_PATH, () => {}); // Delete incomplete file
      console.error('\n❌ Error downloading model:', err.message);
      process.exit(1);
    });
    
  }).on('error', (err) => {
    console.error('❌ Error downloading model:', err.message);
    console.error('💡 Please check your internet connection and try again.');
    process.exit(1);
  });
}

if (require.main === module) {
  downloadModel();
}

module.exports = { downloadModel };

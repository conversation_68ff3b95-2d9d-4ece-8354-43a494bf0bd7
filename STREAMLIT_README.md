# Field ID - Streamlit Version

A simplified Streamlit version of the Field ID AI-powered sports player tracking application.

## 🚀 Quick Start

### Local Development

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the application:**
   ```bash
   streamlit run streamlit_app.py
   ```

3. **Open your browser:**
   - The app will automatically open at `http://localhost:8501`

### Streamlit Cloud Deployment

1. **Fork this repository** to your GitHub account

2. **Deploy to Streamlit Cloud:**
   - Go to [share.streamlit.io](https://share.streamlit.io)
   - Click "New app"
   - Connect your GitHub repository
   - Set main file path: `streamlit_app.py`
   - Click "Deploy"

## 🎯 Features

- **📹 Video Upload:** Drag & drop or browse for sports videos
- **🎯 Player Detection:** Simulated AI player tracking
- **📊 Live Statistics:** Real-time player count and activity metrics
- **🤖 AI Summary:** Intelligent analysis of player movements
- **📥 Data Export:** Download tracking results as JSON

## 📋 Usage

1. **Upload Video:** Click "Browse files" and select a sports video
2. **Start Analysis:** Click "🚀 Start Analysis" to begin processing
3. **View Results:** Watch live statistics update in real-time
4. **Generate Summary:** Click "✨ Generate AI Summary" for insights
5. **Download Data:** Click "📥 Download Results" to save tracking data

## 🛠️ Technical Details

- **Framework:** Streamlit
- **Video Processing:** OpenCV
- **UI Components:** Custom CSS styling
- **File Support:** MP4, AVI, MOV, MKV
- **Max Upload:** 200MB

## 🎨 Features Demonstration

This Streamlit version provides a **demonstration** of the Field ID concept with:

- Simulated player detection (for demo purposes)
- Real-time statistics updates
- Interactive video analysis interface
- AI-generated summaries
- Data export functionality

## 🔧 Configuration

The app can be configured via `.streamlit/config.toml`:

- **Upload limit:** 200MB (adjustable)
- **Theme colors:** Custom blue gradient theme
- **Server settings:** Optimized for performance

## 📱 Responsive Design

The application is fully responsive and works on:
- Desktop computers
- Tablets
- Mobile devices

## 🚀 Deployment Options

### Streamlit Cloud (Recommended)
- Free hosting for public repositories
- Automatic deployments from GitHub
- Built-in SSL and CDN

### Heroku
```bash
# Create Procfile
echo "web: streamlit run streamlit_app.py --server.port=$PORT --server.address=0.0.0.0" > Procfile

# Deploy to Heroku
heroku create your-app-name
git push heroku main
```

### Docker
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8501

CMD ["streamlit", "run", "streamlit_app.py"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🔗 Links

- **Live Demo:** [Your Streamlit App URL]
- **GitHub Repository:** [Your GitHub Repo URL]
- **Documentation:** [Your Docs URL]

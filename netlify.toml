[build]
  command = "npm run build"
  publish = ".next"

[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "8"

[[plugins]]
  package = "@netlify/plugin-nextjs"

[context.production]
  command = "npm run build"

[context.deploy-preview]
  command = "npm run build"

[context.branch-deploy]
  command = "npm run build"

# Headers for ONNX and WASM files
[[headers]]
  for = "/model.onnx"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
    Content-Type = "application/octet-stream"

[[headers]]
  for = "/onnx-wasm/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
    Cross-Origin-Embedder-Policy = "require-corp"
    Cross-Origin-Opener-Policy = "same-origin"

# Redirects for SPA behavior
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  conditions = {Role = ["admin"]}

# Environment variables (set these in Netlify dashboard)
# GOOGLE_GENAI_API_KEY = "your_api_key_here"

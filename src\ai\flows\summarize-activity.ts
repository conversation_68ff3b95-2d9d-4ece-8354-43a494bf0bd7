'use server';
/**
 * @fileOverview A Genkit flow for summarizing player activity from tracking data.
 *
 * - summarizeActivity - A function that generates a summary of player activity.
 * - ActivitySummaryInput - The input type for the summarizeActivity function.
 * - ActivitySummaryOutput - The return type for the summarizeActivity function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const ActivitySummaryInputSchema = z.object({
  trackingDataJson: z
    .string()
    .describe(
      'A JSON string representing the tracking data for all frames. The data is an array of objects, where each object has a "frame" number and a "players" array. Each player object has an "id" and a "box" array [x, y, width, height].'
    ),
    videoDuration: z.number().describe('The total duration of the video in seconds.')
});
export type ActivitySummaryInput = z.infer<typeof ActivitySummaryInputSchema>;

const ActivitySummaryOutputSchema = z.object({
  summary: z.string().describe('A concise summary of the player activity.'),
});
export type ActivitySummaryOutput = z.infer<typeof ActivitySummaryOutputSchema>;

export async function summarizeActivity(input: ActivitySummaryInput): Promise<ActivitySummaryOutput> {
  return summarizeActivityFlow(input);
}

const prompt = ai.definePrompt({
  name: 'summarizeActivityPrompt',
  input: {schema: ActivitySummaryInputSchema},
  output: {schema: ActivitySummaryOutputSchema},
  prompt: `You are a sports analyst AI. Your task is to analyze player tracking data from a video and provide a summary of the activity.

The total video duration is {{{videoDuration}}} seconds.

The tracking data is provided as a JSON string. Each entry represents a frame and contains the players detected in that frame with their bounding boxes.
Analyze the data to identify key insights, such as:
- Which players were present for the longest duration?
- Were there any players who were particularly active (i.e., moved a lot across the frame)?
- Mention the number of unique players tracked.

Here is the tracking data:
{{{trackingDataJson}}}

Provide a concise, insightful summary of the player activity. Be professional and sound like a real sports analyst.
`,
});

const summarizeActivityFlow = ai.defineFlow(
  {
    name: 'summarizeActivityFlow',
    inputSchema: ActivitySummaryInputSchema,
    outputSchema: ActivitySummaryOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
